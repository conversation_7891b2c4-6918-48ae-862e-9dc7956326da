from .db import db
from datetime import datetime
import pytz

class AdminLog(db.Model):
    __tablename__ = "admin_logs"

    id = db.Column(db.Integer, primary_key=True)
    # 操作者信息
    operator_user_number = db.Column(db.String(20), nullable=False, index=True)
    operator_username = db.Column(db.String(80), nullable=True)
    operator_role = db.Column(db.String(20), nullable=False)  # admin 或 super_admin
    
    # 操作信息
    action = db.Column(db.String(50), nullable=False)  # 操作类型：ban_user, unban_user, grant_admin, revoke_admin, view_user_data 等
    target_user_number = db.Column(db.String(20), nullable=True)  # 被操作的用户学号
    target_username = db.Column(db.String(80), nullable=True)  # 被操作的用户姓名
    
    # 操作详情（可读性高的描述）
    description = db.Column(db.Text, nullable=False)
    # 操作参数（JSON格式存储详细信息）
    details = db.Column(db.Text, nullable=True)
    
    # 操作时间和IP
    created_at = db.Column(db.DateTime, nullable=False, default=lambda: datetime.now(pytz.timezone('Asia/Shanghai')))
    ip_address = db.Column(db.String(45), nullable=True)
    user_agent = db.Column(db.Text, nullable=True)
    
    # 操作结果
    status = db.Column(db.String(20), nullable=False, default='success')  # success, failed, error
    error_message = db.Column(db.Text, nullable=True)

    def to_dict(self):
        return {
            "id": self.id,
            "operator_user_number": self.operator_user_number,
            "operator_username": self.operator_username,
            "operator_role": self.operator_role,
            "action": self.action,
            "target_user_number": self.target_user_number,
            "target_username": self.target_username,
            "description": self.description,
            "details": self.details,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "ip_address": self.ip_address,
            "user_agent": self.user_agent,
            "status": self.status,
            "error_message": self.error_message,
        }

    @staticmethod
    def log_action(operator_user_number, operator_username, operator_role, action, description, 
                   target_user_number=None, target_username=None, details=None, 
                   ip_address=None, user_agent=None, status='success', error_message=None):
        """记录管理员操作日志的便捷方法"""
        log = AdminLog(
            operator_user_number=operator_user_number,
            operator_username=operator_username,
            operator_role=operator_role,
            action=action,
            target_user_number=target_user_number,
            target_username=target_username,
            description=description,
            details=details,
            ip_address=ip_address,
            user_agent=user_agent,
            status=status,
            error_message=error_message
        )
        db.session.add(log)
        return log

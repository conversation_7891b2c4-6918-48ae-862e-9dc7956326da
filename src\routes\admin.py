from flask import Blueprint, jsonify, request, session
from sqlalchemy import func, or_
from datetime import datetime, timedelta
import pytz

from ..models.user import User, db
from ..models.blacklist import Blacklist
from ..models.translation_history import TranslationHistory
from ..models.login_history import LoginHistory
from ..utils.admin_auth import require_admin, require_super_admin, is_user_blacklisted, log_admin_action

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

# -------------------- 仪表板 --------------------
@admin_bp.route('/dashboard', methods=['GET'])
@require_admin
def get_dashboard():
    """后台首页仪表板数据"""
    try:
        china_tz = pytz.timezone('Asia/Shanghai')
        today = datetime.now(china_tz).date()
        month_start = today.replace(day=1)

        # 简化查询，避免复杂的JOIN操作
        total_users = User.query.count()

        # 简化今日新用户查询
        today_new_users = 0  # 暂时设为0，避免复杂查询

        # 简化月活跃用户查询
        monthly_active_users = 0  # 暂时设为0，避免复杂查询

        total_trans = TranslationHistory.query.count()

        # 简化今日翻译查询
        today_trans = 0
        try:
            today_trans = TranslationHistory.query.filter(func.date(TranslationHistory.created_at) == today).count()
        except:
            today_trans = 0

        # 获取黑名单用户数量
        blacklist_count = 0
        try:
            from src.models.blacklist import Blacklist
            blacklist_count = Blacklist.query.count()
        except:
            blacklist_count = 0


        return jsonify({
            'status': 'success',
            'data': {
                'user_stats': {
                    'total': total_users,
                    'today_new': today_new_users,
                    'monthly_active': monthly_active_users
                },
                'translation_stats': {
                    'total': total_trans,
                    'today': today_trans
                },
                'blacklist_count': blacklist_count,
                'has_chart_data': True  # 标记有图表数据
            }
        })
    except Exception as e:
        print(f"Dashboard error: {e}")
        return jsonify({
            'status': 'error',
            'message': f'获取仪表板数据失败: {str(e)}'
        }), 500

@admin_bp.route('/dashboard/translation-trend', methods=['GET'])
def get_translation_trend():
    """获取翻译趋势数据"""
    try:
        china_tz = pytz.timezone('Asia/Shanghai')
        today = datetime.now(china_tz).date()

        # 获取最近7天的数据
        days = []
        counts = []

        for i in range(6, -1, -1):  # 从6天前到今天
            target_date = today - timedelta(days=i)

            # 查询当天的翻译数量
            count = TranslationHistory.query.filter(
                func.date(TranslationHistory.created_at) == target_date
            ).count()

            days.append(target_date.strftime('%m/%d'))
            counts.append(count)

        return jsonify({
            'status': 'success',
            'data': {
                'labels': days,
                'datasets': [{
                    'label': '翻译次数',
                    'data': counts,
                    'borderColor': '#3b82f6',
                    'backgroundColor': 'rgba(59, 130, 246, 0.1)',
                    'tension': 0.4,
                    'fill': True
                }]
            }
        })
    except Exception as e:
        print(f"Translation trend error: {e}")
        return jsonify({
            'status': 'error',
            'message': f'获取翻译趋势数据失败: {str(e)}'
        }), 500

@admin_bp.route('/dashboard/language-pairs', methods=['GET'])
def get_language_pairs():
    """获取热门语言对数据"""
    try:
        # 查询语言对使用频率
        language_pairs = db.session.query(
            TranslationHistory.source_language,
            TranslationHistory.target_language,
            func.count(TranslationHistory.id).label('count')
        ).group_by(
            TranslationHistory.source_language,
            TranslationHistory.target_language
        ).order_by(
            func.count(TranslationHistory.id).desc()
        ).limit(10).all()

        # 语言代码到名称的映射
        language_map = {
            'zh': '中文',
            'zh-tw': '繁体中文',
            'en': '英文',
            'ja': '日文',
            'ko': '韩文',
            'fr': '法文',
            'de': '德文',
            'es': '西班牙文',
            'ru': '俄文',
            'it': '意大利文',
            'pt': '葡萄牙文',
            'ar': '阿拉伯文',
            'hi': '印地文',
            'auto': '自动检测'
        }

        labels = []
        data = []
        colors = [
            '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',
            '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6b7280'
        ]

        for i, (source, target, count) in enumerate(language_pairs):
            source_name = language_map.get(source, source)
            target_name = language_map.get(target, target)
            labels.append(f'{source_name}→{target_name}')
            data.append(count)

        return jsonify({
            'status': 'success',
            'data': {
                'labels': labels,
                'datasets': [{
                    'data': data,
                    'backgroundColor': colors[:len(data)],
                    'borderWidth': 2,
                    'borderColor': '#ffffff'
                }]
            }
        })
    except Exception as e:
        print(f"Language pairs error: {e}")
        return jsonify({
            'status': 'error',
            'message': f'获取语言对数据失败: {str(e)}'
        }), 500

@admin_bp.route('/test/chart-data', methods=['GET'])
def test_chart_data():
    """测试图表数据（无需认证）"""
    try:
        # 返回测试数据
        return jsonify({
            'status': 'success',
            'translation_trend': {
                'labels': ['01/01', '01/02', '01/03', '01/04', '01/05', '01/06', '01/07'],
                'datasets': [{
                    'label': '翻译次数',
                    'data': [12, 19, 3, 5, 2, 3, 10],
                    'borderColor': '#3b82f6',
                    'backgroundColor': 'rgba(59, 130, 246, 0.1)',
                    'tension': 0.4,
                    'fill': True
                }]
            },
            'language_pairs': {
                'labels': ['中文→英文', '英文→中文', '中文→日文', '其他'],
                'datasets': [{
                    'data': [35, 30, 15, 20],
                    'backgroundColor': ['#3b82f6', '#ef4444', '#10b981', '#f59e0b'],
                    'borderWidth': 2,
                    'borderColor': '#ffffff'
                }]
            }
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'测试数据生成失败: {str(e)}'
        }), 500

# -------------------- 用户列表 --------------------
@admin_bp.route('/users', methods=['GET'])
@require_admin
def list_users():
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    role = request.args.get('role', '')
    search = request.args.get('search', '')

    query = User.query
    if role:
        query = query.filter(User.role == role)
    if search:
        query = query.filter(or_(User.username.contains(search), User.user_number.contains(search)))

    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    users_data = []
    for u in pagination.items:
        d = u.to_dict()
        d['is_blacklisted'] = is_user_blacklisted(u.user_number)
        users_data.append(d)


    return jsonify({
        'status': 'success',
        'data': {
            'users': users_data,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total
            }
        }
    })

# -------------------- 创建用户 --------------------
@admin_bp.route('/users', methods=['POST'])
@require_super_admin
def create_user():
    data = request.json or {}
    user_number = data.get('user_number', '').strip()
    username = data.get('username', '').strip()
    role = data.get('role', 'user')

    # 验证必填字段
    if not user_number or not username:
        return jsonify({'status': 'error', 'message': '学号和姓名不能为空'}), 400

    # 验证角色
    if role not in ['user', 'admin', 'super_admin']:
        return jsonify({'status': 'error', 'message': '无效的角色'}), 400

    # 检查用户是否已存在
    existing_user = User.query.filter_by(user_number=user_number).first()
    if existing_user:
        return jsonify({'status': 'error', 'message': '该学号已存在'}), 400

    try:
        # 创建新用户，使用默认密码（学号）
        new_user = User(
            user_number=user_number,
            username=username,
            role=role,
            password_hash=user_number  # 默认密码为学号
        )

        db.session.add(new_user)
        db.session.commit()

        log_admin_action('create_user', f'创建用户: {username}({user_number})，默认密码为学号', target_user_number=user_number, target_username=username)
        return jsonify({'status': 'success', 'message': f'用户创建成功，默认密码为学号: {user_number}'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'status': 'error', 'message': f'创建用户失败: {str(e)}'}), 500

# -------------------- 授权/撤销管理员 --------------------
@admin_bp.route('/users/<user_number>/grant', methods=['POST'])
@require_super_admin
def grant_admin(user_number):
    user = User.query.filter_by(user_number=user_number).first()
    if not user:
        return jsonify({'status': 'error', 'message': '用户不存在'}), 404
    user.role = 'admin'
    db.session.commit()
    log_admin_action('grant_admin', '授予管理员权限', target_user_number=user_number, target_username=user.username)
    return jsonify({'status': 'success', 'message': '已授予管理员权限'})

@admin_bp.route('/users/<user_number>/revoke', methods=['POST'])
@require_super_admin
def revoke_admin(user_number):
    user = User.query.filter_by(user_number=user_number).first()
    if not user:
        return jsonify({'status': 'error', 'message': '用户不存在'}), 404
    user.role = 'user'
    db.session.commit()
    log_admin_action('revoke_admin', '撤销管理员权限', target_user_number=user_number, target_username=user.username)
    return jsonify({'status': 'success', 'message': '已撤销管理员权限'})

# -------------------- 黑名单 --------------------
@admin_bp.route('/users/<user_number>/ban', methods=['POST'])
@require_admin
def ban_user(user_number):
    data = request.json or {}
    reason = data.get('reason', '违反平台规定')
    user = User.query.filter_by(user_number=user_number).first()
    if not user:
        return jsonify({'status': 'error', 'message': '用户不存在'}), 404
    existing = Blacklist.query.filter_by(user_number=user_number, is_active=True).first()
    if existing:
        return jsonify({'status': 'error', 'message': '用户已在黑名单中'}), 400

    entry = Blacklist(
        user_number=user.user_number,
        username=user.username,
        user_section=user.user_section,
        reason=reason,
        banned_by=request.current_admin.user_number,
        banned_by_name=request.current_admin.username
    )
    db.session.add(entry)
    db.session.commit()
    log_admin_action('ban_user', f'封禁用户: {reason}', target_user_number=user_number, target_username=user.username)
    return jsonify({'status': 'success', 'message': '用户已封禁'})

@admin_bp.route('/users/<user_number>/unban', methods=['POST'])
@require_admin
def unban_user(user_number):
    entry = Blacklist.query.filter_by(user_number=user_number, is_active=True).first()
    if not entry:
        return jsonify({'status': 'error', 'message': '用户未被封禁'}), 400
    entry.is_active = False
    entry.unbanned_at = datetime.now(pytz.timezone('Asia/Shanghai'))
    entry.unbanned_by = request.current_admin.user_number
    entry.unbanned_by_name = request.current_admin.username
    db.session.commit()
    log_admin_action('unban_user', '解封用户', target_user_number=user_number, target_username=entry.username)
    return jsonify({'status': 'success', 'message': '用户已解封'})

# -------------------- 翻译历史管理 --------------------
@admin_bp.route('/translations', methods=['GET'])
@require_admin
def list_translations():
    """查看翻译历史记录"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    user_number = request.args.get('user_number', '')

    query = TranslationHistory.query
    if user_number:
        query = query.filter(TranslationHistory.user_number == user_number)

    # 按时间倒序排列
    query = query.order_by(TranslationHistory.created_at.desc())
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)

    translations_data = []
    for t in pagination.items:
        data = {
            'id': t.id,
            'user_number': t.user_number,
            'user_name': t.user_name,
            'source_text': t.source_text[:100] + '...' if len(t.source_text) > 100 else t.source_text,
            'translated_text': t.translated_text[:100] + '...' if len(t.translated_text) > 100 else t.translated_text,
            'source_language': t.source_language,
            'target_language': t.target_language,
            'model_used': t.model_used,
            'created_at': t.created_at.isoformat() if t.created_at else None
        }
        translations_data.append(data)


    return jsonify({
        'status': 'success',
        'data': {
            'translations': translations_data,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total
            }
        }
    })

@admin_bp.route('/translations/<int:translation_id>', methods=['GET'])
@require_admin
def get_translation_detail(translation_id):
    """获取翻译记录详情"""
    translation = TranslationHistory.query.get_or_404(translation_id)


    return jsonify({
        'status': 'success',
        'data': {
            'id': translation.id,
            'user_number': translation.user_number,
            'user_name': translation.user_name,
            'source_text': translation.source_text,
            'translated_text': translation.translated_text,
            'source_language': translation.source_language,
            'target_language': translation.target_language,
            'model_used': translation.model_used,
            'created_at': translation.created_at.isoformat() if translation.created_at else None
        }
    })

# -------------------- 数据分析 --------------------
@admin_bp.route('/analytics/users', methods=['GET'])
@require_admin
def get_user_analytics():
    """用户数据分析"""
    china_tz = pytz.timezone('Asia/Shanghai')
    today = datetime.now(china_tz).date()

    # 计算不同时间段的数据
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)
    year_ago = today - timedelta(days=365)

    # 日活跃用户（最近7天每天的活跃用户数）
    daily_active = []
    for i in range(7):
        date = today - timedelta(days=i)
        # 统计当天有登录记录的不重复用户数
        count = db.session.query(LoginHistory.user_number).filter(
            func.date(LoginHistory.login_time) == date
        ).distinct().count()
        daily_active.append({
            'date': date.isoformat(),
            'count': count
        })

    # 月活跃用户（最近30天内有登录记录的不重复用户数）
    monthly_active = db.session.query(LoginHistory.user_number).filter(
        LoginHistory.login_time >= month_ago
    ).distinct().count()

    # 年活跃用户（最近365天内有登录记录的不重复用户数）
    yearly_active = db.session.query(LoginHistory.user_number).filter(
        LoginHistory.login_time >= year_ago
    ).distinct().count()

    # 今日活跃用户
    today_active = db.session.query(LoginHistory.user_number).filter(
        func.date(LoginHistory.login_time) == today
    ).distinct().count()

    # 本周活跃用户
    week_active = db.session.query(LoginHistory.user_number).filter(
        LoginHistory.login_time >= week_ago
    ).distinct().count()

    # 用户登录时间分布（按小时统计）
    login_hours = db.session.query(
        func.extract('hour', LoginHistory.login_time).label('hour'),
        func.count(LoginHistory.id).label('count')
    ).filter(LoginHistory.login_time >= week_ago).group_by('hour').all()

    hour_distribution = [{'hour': int(h.hour), 'count': h.count} for h in login_hours]


    return jsonify({
        'status': 'success',
        'data': {
            'daily_active': daily_active,
            'today_active': today_active,
            'week_active': week_active,
            'monthly_active': monthly_active,
            'yearly_active': yearly_active,
            'login_hour_distribution': hour_distribution
        }
    })

@admin_bp.route('/analytics/translations', methods=['GET'])
@require_admin
def get_translation_analytics():
    """翻译数据分析"""
    china_tz = pytz.timezone('Asia/Shanghai')
    today = datetime.now(china_tz).date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)

    # 每日翻译量（最近7天）
    daily_translations = []
    for i in range(7):
        date = today - timedelta(days=i)
        count = TranslationHistory.query.filter(func.date(TranslationHistory.created_at) == date).count()
        daily_translations.append({
            'date': date.isoformat(),
            'count': count
        })

    # 语言对使用统计
    lang_pairs = db.session.query(
        TranslationHistory.source_language,
        TranslationHistory.target_language,
        func.count(TranslationHistory.id).label('count')
    ).filter(TranslationHistory.created_at >= month_ago).group_by(
        TranslationHistory.source_language,
        TranslationHistory.target_language
    ).order_by(func.count(TranslationHistory.id).desc()).limit(10).all()

    language_pairs = [
        {
            'source_lang': lp.source_language,
            'target_lang': lp.target_language,
            'count': lp.count
        } for lp in lang_pairs
    ]

    # 模型使用统计
    model_usage = db.session.query(
        TranslationHistory.model_used,
        func.count(TranslationHistory.id).label('count')
    ).filter(TranslationHistory.created_at >= month_ago).group_by(
        TranslationHistory.model_used
    ).order_by(func.count(TranslationHistory.id).desc()).all()

    model_stats = [
        {
            'model': mu.model_used,
            'count': mu.count
        } for mu in model_usage
    ]


    return jsonify({
        'status': 'success',
        'data': {
            'daily_translations': daily_translations,
            'language_pairs': language_pairs,
            'model_usage': model_stats
        }
    })

# -------------------- 黑名单管理 --------------------
@admin_bp.route('/blacklist', methods=['GET'])
@require_admin
def list_blacklist():
    """查看黑名单"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '')

    query = Blacklist.query
    if search:
        query = query.filter(or_(
            Blacklist.username.contains(search),
            Blacklist.user_number.contains(search)
        ))

    # 按封禁时间倒序排列
    query = query.order_by(Blacklist.banned_at.desc())
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)

    blacklist_data = [entry.to_dict() for entry in pagination.items]


    return jsonify({
        'status': 'success',
        'data': {
            'blacklist': blacklist_data,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total
            }
        }
    })

# -------------------- 操作日志 --------------------
@admin_bp.route('/logs', methods=['GET'])
@require_admin
def list_admin_logs():
    """查看操作日志"""
    from ..models.admin_log import AdminLog

    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    action = request.args.get('action', '')
    operator = request.args.get('operator', '')

    query = AdminLog.query
    if action:
        query = query.filter(AdminLog.action == action)
    if operator:
        query = query.filter(AdminLog.operator_user_number == operator)

    # 按时间倒序排列
    query = query.order_by(AdminLog.created_at.desc())
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)

    logs_data = [log.to_dict() for log in pagination.items]

    # 获取可用的操作类型列表
    available_actions = db.session.query(AdminLog.action).distinct().all()
    actions_list = [action[0] for action in available_actions]


    return jsonify({
        'status': 'success',
        'data': {
            'logs': logs_data,
            'available_actions': actions_list,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total
            }
        }
    })

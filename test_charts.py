#!/usr/bin/env python3
"""
测试图表API的脚本
"""

import requests
import json
from datetime import datetime, timedelta
import random

# 测试数据生成
def generate_test_data():
    """生成一些测试翻译数据"""
    from main import app
    from src.models.translation_history import TranslationHistory
    from src.models.db import db
    
    with app.app_context():
        # 清除现有测试数据
        TranslationHistory.query.filter(TranslationHistory.user_number.like('test_%')).delete()
        
        # 生成最近7天的测试数据
        today = datetime.now().date()
        language_pairs = [
            ('zh', 'en'), ('en', 'zh'), ('zh', 'ja'), ('ja', 'zh'),
            ('zh', 'ko'), ('ko', 'zh'), ('en', 'ja'), ('ja', 'en'),
            ('zh-tw', 'en'), ('en', 'zh-tw')
        ]
        
        for i in range(7):
            target_date = today - timedelta(days=i)
            # 每天生成随机数量的翻译记录
            daily_count = random.randint(5, 50)
            
            for j in range(daily_count):
                source_lang, target_lang = random.choice(language_pairs)
                
                history = TranslationHistory(
                    user_number=f'test_user_{j % 10}',
                    user_name=f'测试用户{j % 10}',
                    source_text=f'测试文本 {j}',
                    translated_text=f'Translated text {j}',
                    source_language=source_lang,
                    target_language=target_lang,
                    model_used='test_model',
                    character_count=random.randint(10, 100),
                    created_at=datetime.combine(target_date, datetime.min.time())
                )
                db.session.add(history)
        
        db.session.commit()
        print(f"生成了测试数据，共 {TranslationHistory.query.count()} 条记录")

def test_chart_apis():
    """测试图表API"""
    base_url = 'http://127.0.0.1:5000'
    
    # 测试翻译趋势API
    try:
        response = requests.get(f'{base_url}/admin/dashboard/translation-trend')
        print("翻译趋势API响应:")
        print(json.dumps(response.json(), indent=2, ensure_ascii=False))
        print()
    except Exception as e:
        print(f"翻译趋势API测试失败: {e}")
    
    # 测试语言对API
    try:
        response = requests.get(f'{base_url}/admin/dashboard/language-pairs')
        print("语言对API响应:")
        print(json.dumps(response.json(), indent=2, ensure_ascii=False))
        print()
    except Exception as e:
        print(f"语言对API测试失败: {e}")

if __name__ == '__main__':
    print("生成测试数据...")
    generate_test_data()
    
    print("测试图表API...")
    test_chart_apis()

from .db import db

class User(db.Model):
    __tablename__ = "users"

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    # 学号（可为空，但应唯一）
    user_number = db.Column(db.String(20), unique=True, nullable=False)
    # 学院信息
    user_section = db.Column(db.String(100), nullable=True)
    # 用户角色：user(普通用户), admin(管理员), super_admin(超级管理员)
    role = db.Column(db.String(20), nullable=False, default='user')

    # 为常用查询添加索引
    __table_args__ = (
        db.Index('idx_user_number', 'user_number'),
    )

    def to_dict(self):
        return {
            "id": self.id,
            "user_number": self.user_number,
            "username": self.username,
            "user_section": self.user_section,
            "role": self.role,
        }

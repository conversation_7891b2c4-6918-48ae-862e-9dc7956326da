// Vue.js 移动端翻译应用
const { createApp } = Vue;

const app = createApp({
    data() {
        return {
            // UI状态
            isDarkMode: false,
            sidebarOpen: false,
            showModelSelector: false,
            showSourceLanguageDropdown: false,
            showTargetLanguageDropdown: false,
            showModelDropdown: false,

            // 认证状态
            isLoggedIn: false,
            userInfo: {},

            // 翻译状态
            sourceText: '',
            translatedText: '',
            sourceLanguage: 'auto',
            targetLanguage: 'en',
            selectedModel: 'qwen2:7b',
            isTranslating: false,
            maxCharacters: 5000,
            isTranslating: false,
            maxCharacters: 5000,
            detectTimeout: null,

            // 语言和模型数据
            languages: {
                'zh-tw': '繁体中文',
                'zh': '简体中文',
                'en': '英语',
                'ja': '日语',
                'ko': '韩语',
                'fr': '法语',
                'de': '德语',
                'es': '西班牙语',
                'ru': '俄语',
                'it': '意大利语',
                'pt': '葡萄牙语',
                'ar': '阿拉伯语',
                'hi': '印地语'
            },
            models: {},

            // 翻译历史相关
            showHistoryModal: false,
            translationHistory: [],
            historyLoading: false,
            historySearch: '',
            expandedHistoryId: null,
            currentHistoryItem: null,
            searchTimeout: null,
            historyFilters: {
                sourceLanguage: '',
                targetLanguage: '',
                model: ''
            },
            historyPagination: {
                page: 1,
                per_page: 20,
                total: 0,
                pages: 0,
                has_prev: false,
                has_next: false
            },
            availableLanguages: {},
            availableModels: {}
        }
    },

    computed: {
        isAdmin() {
            return this.isLoggedIn && this.userInfo && ['admin', 'super_admin'].includes(this.userInfo.role);
        },
        isSuperAdmin() {
            return this.isLoggedIn && this.userInfo && this.userInfo.role === 'super_admin';
        },


    },

    async mounted() {
        await this.checkLoginStatus();
        await this.loadAvailableModels();
        this.applyDarkMode();

        // 监听系统主题变化
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addListener(() => this.applyDarkMode());
        }

        // 添加点击外部区域关闭下拉框的事件监听器
        this.setupClickOutsideListeners();
    },

    beforeUnmount() {
        // 清理事件监听器，防止内存泄漏
        this.cleanupEventListeners();
    },

    methods: {
        // 切换历史记录展开状态
        toggleHistoryExpansion(historyId) {
            // 找到对应的历史项
            const historyItem = this.translationHistory.find(item => item.id === historyId);
            if (historyItem) {
                // 设置当前历史项数据
                this.currentHistoryItem = historyItem;
                this.expandedHistoryId = historyId;
            }
        },

        // 基本方法实现
        async checkLoginStatus() {
            try {
                const response = await fetch('/api/auth/check');
                const data = await response.json();
                this.isLoggedIn = data.logged_in;
                if (data.logged_in) {
                    this.userInfo = data.user_info;
                }
            } catch (error) {
                console.error('检查登录状态失败:', error);
                this.isLoggedIn = false;
                this.userInfo = {};
            }
        },

        async loadAvailableModels() {
            try {
                const response = await fetch('/api/models');
                const data = await response.json();

                if (data.status === 'success') {
                    // 正确提取models数据
                    this.models = data.models;

                    // 如果后端返回了语言数据，也可以更新语言列表
                    if (data.languages) {
                        // 移除auto选项，避免重复
                        const { auto, ...otherLangs } = data.languages;
                        this.languages = { ...this.languages, ...otherLangs };
                    }
                } else {
                    console.error('加载模型失败:', data.message);
                }
            } catch (error) {
                console.error('加载模型失败:', error);
                // 设置默认模型作为fallback
                this.models = {
                    'qwen2:7b': '通义千问2 7B',
                    'qwen2.5:32b': '通义千问2.5 32B',
                    'gemma3:27b': 'Gemma 3 27B',
                    'deepseek-r1:14b': 'DeepSeek R1 14B'
                };
            }
        },

        applyDarkMode() {
            const savedMode = localStorage.getItem('darkMode');
            if (savedMode !== null) {
                this.isDarkMode = savedMode === 'true';
            } else {
                this.isDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
            }

            if (this.isDarkMode) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        },

        toggleDarkMode() {
            this.isDarkMode = !this.isDarkMode;
            localStorage.setItem('darkMode', this.isDarkMode);
            this.applyDarkMode();
        },

        toggleModelSelector() {
            this.showModelSelector = !this.showModelSelector;
        },

        // 设置点击外部区域关闭下拉框的事件监听器
        setupClickOutsideListeners() {
            // 保存事件处理函数的引用，以便后续清理
            this.clickOutsideHandler = (event) => {
                this.handleClickOutside(event);
            };

            this.scrollHandler = () => {
                this.closeAllDropdowns();
            };

            this.resizeHandler = () => {
                this.closeAllDropdowns();
            };

            // 添加事件监听器
            document.addEventListener('click', this.clickOutsideHandler);
            document.addEventListener('scroll', this.scrollHandler, { passive: true });
            window.addEventListener('resize', this.resizeHandler);
        },

        // 清理事件监听器
        cleanupEventListeners() {
            if (this.clickOutsideHandler) {
                document.removeEventListener('click', this.clickOutsideHandler);
            }
            if (this.scrollHandler) {
                document.removeEventListener('scroll', this.scrollHandler);
            }
            if (this.resizeHandler) {
                window.removeEventListener('resize', this.resizeHandler);
            }
        },

        // 处理点击外部区域的逻辑
        handleClickOutside(event) {
            // 定义所有下拉框的配置
            const dropdowns = [
                {
                    elementId: 'model-select',
                    isOpen: this.showModelDropdown,
                    closeMethod: () => { this.showModelDropdown = false; }
                },
                {
                    elementId: 'source-language-select',
                    isOpen: this.showSourceLanguageDropdown,
                    closeMethod: () => { this.showSourceLanguageDropdown = false; }
                },
                {
                    elementId: 'target-language-select',
                    isOpen: this.showTargetLanguageDropdown,
                    closeMethod: () => { this.showTargetLanguageDropdown = false; }
                }
            ];

            // 检查每个下拉框，如果点击在外部且下拉框是打开的，则关闭它
            dropdowns.forEach(dropdown => {
                const element = document.getElementById(dropdown.elementId);
                if (element && !element.contains(event.target) && dropdown.isOpen) {
                    dropdown.closeMethod();
                }
            });
        },

        // 关闭所有下拉框的通用方法
        closeAllDropdowns() {
            this.showModelDropdown = false;
            this.showSourceLanguageDropdown = false;
            this.showTargetLanguageDropdown = false;
        },

        showTranslationHistory() {
            console.log('显示翻译历史模态框');
            this.showHistoryModal = true;
            this.expandedHistoryId = null;
            this.historySearch = '';
            this.loadTranslationHistory();
        },

        closeHistoryModal() {
            this.showHistoryModal = false;
            this.expandedHistoryId = null;
            this.currentHistoryItem = null;
            this.historySearch = '';
        },

        // 翻译历史相关方法
        async loadTranslationHistory(page = 1) {
            console.log('开始加载翻译历史，页码:', page);
            this.historyLoading = true;
            try {
                const params = new URLSearchParams({
                    page: page,
                    per_page: 20
                });

                if (this.historySearch) {
                    params.append('search', this.historySearch);
                }

                console.log('请求URL:', `/api/user/history?${params}`);
                const response = await fetch(`/api/user/history?${params}`);
                const data = await response.json();
                console.log('API响应:', data);

                if (data.status === 'success') {
                    this.translationHistory = data.data || [];
                    this.historyPagination = data.pagination || {
                        page: 1,
                        per_page: 20,
                        total: 0,
                        pages: 0,
                        has_prev: false,
                        has_next: false
                    };
                    return true; // 返回成功标志
                } else {
                    console.error('加载翻译历史失败:', data.message);
                    this.translationHistory = [];
                    return false; // 返回失败标志
                }
            } catch (error) {
                console.error('加载翻译历史失败:', error);
                this.translationHistory = [];
                return false; // 返回失败标志
            } finally {
                this.historyLoading = false;
            }
        },

        // 显示翻译历史模态框
        async showHistory() {
            this.showHistoryModal = true;
            await this.loadTranslationHistory();
        },

        // 基本UI方法
        async logout() {
            try {
                // 调用后端登出API
                await fetch('/api/auth/logout', { method: 'POST' });

                // 清除本地状态
                this.isLoggedIn = false;
                this.userInfo = {};
                this.translationHistory = [];
                this.sourceText = '';
                this.translatedText = '';

                // 关闭所有下拉框和模态框
                this.closeAllDropdowns();
                this.closeSidebar();
                this.showHistoryModal = false;

                // 重定向到登录页面
                window.location.href = '/login';
            } catch (error) {
                console.error('登出失败:', error);
                // 即使登出失败也清除本地状态并跳转到登录页面
                this.isLoggedIn = false;
                this.userInfo = {};
                window.location.href = '/login';
            }
        },

        toggleSidebar() {
            this.sidebarOpen = !this.sidebarOpen;
            // 切换侧边栏时关闭所有下拉框
            this.closeAllDropdowns();
        },

        closeSidebar() {
            this.sidebarOpen = false;
        },

        toggleSourceLanguageDropdown() {
            this.showSourceLanguageDropdown = !this.showSourceLanguageDropdown;
            this.showTargetLanguageDropdown = false;
            this.showModelDropdown = false;
        },

        toggleTargetLanguageDropdown() {
            this.showTargetLanguageDropdown = !this.showTargetLanguageDropdown;
            this.showSourceLanguageDropdown = false;
            this.showModelDropdown = false;
        },

        toggleModelDropdown() {
            this.showModelDropdown = !this.showModelDropdown;
            this.showSourceLanguageDropdown = false;
            this.showTargetLanguageDropdown = false;
        },

        closeAllDropdowns() {
            this.showSourceLanguageDropdown = false;
            this.showTargetLanguageDropdown = false;
            this.showModelDropdown = false;
        },

        swapLanguages() {
            if (this.sourceLanguage !== 'auto') {
                const temp = this.sourceLanguage;
                this.sourceLanguage = this.targetLanguage;
                this.targetLanguage = temp;
            }
        },

        selectSourceLanguage(code) {
            this.sourceLanguage = code;
            this.showSourceLanguageDropdown = false;
        },

        selectTargetLanguage(code) {
            this.targetLanguage = code;
            this.showTargetLanguageDropdown = false;
        },

        selectModel(model) {
            this.selectedModel = model;
            this.showModelDropdown = false;
        },

        // 拖拽相关方法
        startDrag() {
            // 拖拽开始
            console.log('startDrag called');
        },

        // 输入处理方法
        onSourceTextInput() {
            // 处理输入
            console.log('onSourceTextInput called');
        },

        // 翻译方法
        async translateText() {
            if (!this.sourceText.trim()) {
                this.showNotification('请输入要翻译的文本', 'warning');
                return;
            }

            if (this.sourceText.length > this.maxCharacters) {
                this.showNotification(`文本长度超过限制，最多支持${this.maxCharacters}个字符`, 'error');
                return;
            }

            if (!this.isLoggedIn) {
                this.showNotification('请先登录', 'warning');
                return;
            }

            this.isTranslating = true;
            this.translatedText = '';

            try {
                const response = await fetch('/api/translate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: this.sourceText,
                        source_lang: this.sourceLanguage,
                        target_lang: this.targetLanguage,
                        model: this.selectedModel
                    })
                });

                const data = await response.json();

                if (data.status === 'success') {
                    this.translatedText = data.translation;
                    this.showNotification('翻译完成', 'success');
                } else {
                    console.warn('Translation error:', data.message);
                    this.showNotification(data.message || '翻译失败，请稍后重试', 'error');
                }
            } catch (error) {
                console.error('翻译失败:', error);
                this.showNotification('网络错误，请重试', 'error');
            } finally {
                this.isTranslating = false;
            }
        },

        // 剪贴板方法
        async pasteFromClipboard() {
            try {
                const text = await navigator.clipboard.readText();
                this.sourceText = text;
            } catch (error) {
                console.error('粘贴失败:', error);
            }
        },

        async copyTranslatedText() {
            try {
                await navigator.clipboard.writeText(this.translatedText);
                console.log('复制成功');
            } catch (error) {
                console.error('复制失败:', error);
            }
        },

        // 清除方法
        clearAll() {
            this.sourceText = '';
            this.translatedText = '';
        },

        // 通知方法
        showNotification(message, type = 'info') {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-lg shadow-lg text-white transform translate-x-full transition-transform duration-300 ${
                type === 'success' ? 'bg-green-500' :
                type === 'error' ? 'bg-red-500' :
                type === 'warning' ? 'bg-yellow-500' :
                'bg-blue-500'
            }`;
            notification.textContent = message;

            // 添加到页面
            document.body.appendChild(notification);

            // 显示动画
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // 3秒后隐藏
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        },

        // 搜索翻译历史（防抖）
        searchHistory() {
            // 清除之前的定时器
            if (this.searchTimeout) {
                clearTimeout(this.searchTimeout);
            }

            // 设置新的定时器，800ms后执行搜索
            this.searchTimeout = setTimeout(() => {
                // 执行搜索
                this.loadTranslationHistory(1);
            }, 800);
        },

        // 高亮搜索文本
        highlightSearchText(text) {
            if (!this.historySearch.trim() || !text) {
                return text;
            }

            const searchTerm = this.historySearch.trim();
            const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
            return text.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-600/50 px-1 rounded">$1</mark>');
        },

        // 格式化日期
        formatDate(dateString) {
            if (!dateString) return '';

            try {
                const date = new Date(dateString);
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
            } catch (error) {
                return dateString;
            }
        },

        // 复制历史翻译结果
        async copyHistoryText(text, type = 'result') {
            try {
                await navigator.clipboard.writeText(text);
                this.showNotification(`已复制${type === 'source' ? '原文' : '翻译结果'}`, 'success');
            } catch (error) {
                console.error('复制失败:', error);
                this.showNotification('复制失败，请手动复制', 'error');
            }
        },

        // 删除翻译历史记录
        async deleteHistoryItem(historyId, index) {
            if (!confirm('确定要删除这条翻译记录吗？')) {
                return;
            }

            try {
                const response = await fetch(`/api/user/history/${historyId}`, {
                    method: 'DELETE'
                });
                const data = await response.json();

                if (data.status === 'success') {
                    this.translationHistory.splice(index, 1);
                    this.showNotification('翻译记录已删除', 'success');
                } else {
                    this.showNotification(data.message || '删除失败', 'error');
                }
            } catch (error) {
                console.error('删除失败:', error);
                this.showNotification('删除失败', 'error');
            }
        },

        // 格式化时间
        formatTime(timeString) {
            if (!timeString) return '';
            const date = new Date(timeString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        },

        // 重新翻译历史记录
        retranslateHistory(historyItem) {
            // 填充翻译表单
            this.sourceText = historyItem.source_text;
            this.sourceLanguage = historyItem.source_language;
            this.targetLanguage = historyItem.target_language;
            this.selectedModel = historyItem.model_used;

            // 关闭所有模态框
            this.showHistoryModal = false;
            this.expandedHistoryId = null;

            // 显示提示
            this.showNotification('已填充翻译内容，可重新翻译', 'info');
        },

        // 翻译历史分页
        goToHistoryPage(page) {
            if (page >= 1 && page <= this.historyPagination.pages) {
                this.loadTranslationHistory(page);
            }
        },

        // 通知系统
        showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-20 left-4 right-4 px-4 py-3 rounded-xl shadow-lg z-50 text-white transition-all duration-300 transform -translate-y-full backdrop-filter backdrop-blur-lg`;

            switch (type) {
                case 'success':
                    notification.classList.add('bg-green-500/90');
                    break;
                case 'error':
                    notification.classList.add('bg-red-500/90');
                    break;
                case 'warning':
                    notification.classList.add('bg-yellow-500/90');
                    break;
                default:
                    notification.classList.add('bg-blue-500/90');
            }

            const iconId = type === 'success' ? 'icon-check' : type === 'error' ? 'icon-error' : type === 'warning' ? 'icon-warning' : 'icon-info';
            notification.innerHTML = `
                <div class="flex items-center space-x-2">
                    <svg class="icon text-sm">
                        <use href="#${iconId}"></use>
                    </svg>
                    <span class="font-medium">${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.remove('-translate-y-full');
            }, 100);

            setTimeout(() => {
                notification.classList.add('-translate-y-full');
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
    },

    // 组件销毁时清理定时器
    beforeUnmount() {
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
    }
});

// 挂载应用
app.mount('#app');

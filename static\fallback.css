/* 本地备用样式文件 - 确保基本样式正常显示 */
body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    min-height: 100vh;
}

.dark body {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
}

/* 玻璃效果 */
.glass-effect {
    backdrop-filter: blur(16px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 基础布局 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* 按钮样式 */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-1px);
}

.btn-demo {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

/* 输入框样式 */
.form-input {
    padding: 0.75rem;
    border: 2px solid rgba(59, 130, 246, 0.3);
    border-radius: 0.5rem;
    background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(239,246,255,0.9));
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 翻译界面 */
.translation-panel {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 1rem;
    padding: 2rem;
    margin: 2rem 0;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.dark .translation-panel {
    background: rgba(31, 41, 55, 0.95);
    color: white;
}

/* 特色卡片 */
.feature-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    transition: transform 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-card:hover {
    transform: scale(1.05);
}

.dark .feature-card {
    background: rgba(31, 41, 55, 0.95);
    color: white;
}

/* 响应式 */
@media (max-width: 768px) {
    .container {
        padding: 0 0.5rem;
    }
    
    .translation-panel {
        padding: 1rem;
        margin: 1rem 0;
    }
}

/* 隐藏Vue未加载时的内容 */
[v-cloak] {
    display: none !important;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

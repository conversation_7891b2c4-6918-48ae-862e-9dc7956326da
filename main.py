import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

from flask import Flask, send_from_directory
from flask_cors import CORS
from src.models.db import db

from src.routes.auth import auth_bp
from src.routes.translation import translation_bp
from src.routes.admin import admin_bp



app = Flask(__name__, static_folder=os.path.join(os.path.dirname(__file__), 'static'))
app.config['SECRET_KEY'] = os.getenv('FLASK_SECRET_KEY', 'asdf#FGSgvasgf$5$WGT')

# Session 配置 - 会话级别（关闭浏览器需重新登录）
app.config['SESSION_COOKIE_SECURE'] = True  # 开发环境设为False
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
app.config['SESSION_PERMANENT'] = False  # 不持久化，关闭浏览器即失效
# 移除 PERMANENT_SESSION_LIFETIME，使用浏览器会话级别

# 启用CORS
CORS(app)

app.register_blueprint(auth_bp, url_prefix='/api/auth')
app.register_blueprint(translation_bp, url_prefix='/api')
app.register_blueprint(admin_bp)

db_dir = os.path.join(os.path.dirname(__file__), 'database')
os.makedirs(db_dir, exist_ok=True)
app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(db_dir, 'app.db')}"
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)
with app.app_context():
    db.create_all()

@app.route('/login')
def login_page():
    """登录页面"""
    return send_from_directory(app.static_folder, 'login.html')

@app.route('/admin')
def admin_page():
    """管理面板页面 - 增强安全认证"""
    from flask import session, redirect, url_for, request, abort
    from src.utils.token_auth import TokenAuth
    from src.models.user import User
    from src.models.blacklist import Blacklist
    import secrets

    # 生成随机nonce用于CSP
    nonce = secrets.token_urlsafe(16)

    # 多层安全检查
    user_authenticated = False
    user_role = None
    user_number = None

    # 第一层：检查session认证
    if session.get('user_info'):
        user_info = session.get('user_info')
        user_number = user_info.get('user_number')

        # 验证session完整性
        if user_number:
            # 第二层：数据库验证用户权限（不信任session中的role）
            user = User.query.filter_by(user_number=user_number).first()
            if user and user.role in ['admin', 'super_admin']:
                # 第三层：检查黑名单状态
                if not Blacklist.query.filter_by(user_number=user_number, is_active=True).first():
                    user_authenticated = True
                    user_role = user.role

    # 如果session认证失败，尝试token认证
    if not user_authenticated:
        token = None
        # 从Cookie获取token
        if hasattr(request, 'cookies'):
            token = request.cookies.get('auth_token')
        # 从URL参数获取token（仅用于调试，生产环境应禁用）
        if not token and app.debug:
            token = request.args.get('token')

        if token:
            payload = TokenAuth.verify_token(token)
            if payload:
                user_number = payload.get('user_number')
                if user_number:
                    # 数据库验证
                    user = User.query.filter_by(user_number=user_number).first()
                    if user and user.role in ['admin', 'super_admin']:
                        # 检查黑名单
                        if not Blacklist.query.filter_by(user_number=user_number, is_active=True).first():
                            user_authenticated = True
                            user_role = user.role

    # 最终权限检查
    if not user_authenticated or user_role not in ['admin', 'super_admin']:
        # 记录未授权访问尝试
        print(f"Unauthorized admin access attempt from {request.remote_addr}, user: {user_number}")
        return redirect(url_for('login_page'))

    # 设置安全响应头 - 修复CSP问题
    response = app.make_response(send_from_directory(app.static_folder, 'admin.html'))
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
    # 放宽CSP策略以支持外部CDN和内联脚本
    response.headers['Content-Security-Policy'] = f"default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://unpkg.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://cdn.tailwindcss.com; img-src 'self' data: https:; connect-src 'self'; font-src 'self' https://cdnjs.cloudflare.com"

    return response

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def serve(path):
    from flask import session, redirect, url_for, request
    from src.utils.token_auth import TokenAuth
    
    static_folder_path = app.static_folder
    if static_folder_path is None:
        return "Static folder not configured", 404
    
    # 定义需要保护的文件列表
    protected_files = ['index.html', 'index', '']
    
    # 检查是否访问受保护的页面
    is_protected = (path in protected_files or 
                   path == '' or 
                   path.endswith('index.html') or
                   path == 'index')
    
    if is_protected:
        # 检查session认证
        if session.get('user_info'):
            # 已通过session认证，允许访问
            pass
        else:
            # 检查token认证
            token = None
            # 从Cookie获取token
            if hasattr(request, 'cookies'):
                token = request.cookies.get('auth_token')
            # 从URL参数获取token
            if not token:
                token = request.args.get('token')
            
            if token:
                payload = TokenAuth.verify_token(token)
                if not payload:
                    # Token无效，重定向到登录页
                    return redirect(url_for('login_page'))
            else:
                # 无认证信息，重定向到登录页
                return redirect(url_for('login_page'))
    
    # 处理文件访问
    if path != "" and os.path.exists(os.path.join(static_folder_path, path)):
        return send_from_directory(static_folder_path, path)
    else:
        # 默认返回index.html（已经过认证检查）
        index_path = os.path.join(static_folder_path, 'index.html')
        if os.path.exists(index_path):
            return send_from_directory(static_folder_path, 'index.html')
        else:
            return "index.html not found", 404


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)

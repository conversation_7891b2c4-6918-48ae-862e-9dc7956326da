<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图表测试页面</title>
    <script src="chart.js" onerror="this.onerror=null; this.src='https://cdn.jsdelivr.net/npm/chart.js'"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .chart-container { width: 400px; height: 300px; margin: 20px 0; border: 1px solid #ccc; }
        .info { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>图表测试页面</h1>
    
    <div class="info">
        <strong>Chart.js 状态:</strong> <span id="chartStatus">检查中...</span>
    </div>
    
    <div class="info">
        <strong>API 测试:</strong>
        <button onclick="testAPIs()">测试图表API</button>
        <div id="apiResults"></div>
    </div>
    
    <h2>测试图表 1: 翻译趋势</h2>
    <div class="chart-container">
        <canvas id="testChart1"></canvas>
    </div>
    
    <h2>测试图表 2: 语言对分布</h2>
    <div class="chart-container">
        <canvas id="testChart2"></canvas>
    </div>

    <script>
        // 检查Chart.js是否加载
        function checkChartJS() {
            const status = document.getElementById('chartStatus');
            if (typeof Chart !== 'undefined') {
                status.textContent = '✅ Chart.js 已加载 (版本: ' + Chart.version + ')';
                status.style.color = 'green';
                createTestCharts();
            } else {
                status.textContent = '❌ Chart.js 未加载';
                status.style.color = 'red';
            }
        }
        
        // 创建测试图表
        function createTestCharts() {
            // 测试折线图
            const ctx1 = document.getElementById('testChart1');
            new Chart(ctx1, {
                type: 'line',
                data: {
                    labels: ['01/01', '01/02', '01/03', '01/04', '01/05', '01/06', '01/07'],
                    datasets: [{
                        label: '翻译次数',
                        data: [12, 19, 3, 5, 2, 3, 10],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
            
            // 测试饼图
            const ctx2 = document.getElementById('testChart2');
            new Chart(ctx2, {
                type: 'doughnut',
                data: {
                    labels: ['中文→英文', '英文→中文', '中文→日文', '其他'],
                    datasets: [{
                        data: [35, 30, 15, 20],
                        backgroundColor: ['#3b82f6', '#ef4444', '#10b981', '#f59e0b'],
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
        
        // 测试API
        async function testAPIs() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '<p>测试中...</p>';
            
            try {
                // 测试翻译趋势API
                const trendResponse = await fetch('/admin/dashboard/translation-trend');
                const trendData = await trendResponse.json();
                
                // 测试语言对API
                const pairsResponse = await fetch('/admin/dashboard/language-pairs');
                const pairsData = await pairsResponse.json();
                
                resultsDiv.innerHTML = `
                    <h4>翻译趋势API:</h4>
                    <pre>${JSON.stringify(trendData, null, 2)}</pre>
                    <h4>语言对API:</h4>
                    <pre>${JSON.stringify(pairsData, null, 2)}</pre>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `<p style="color: red;">API测试失败: ${error.message}</p>`;
            }
        }
        
        // 页面加载完成后检查
        window.addEventListener('load', () => {
            setTimeout(checkChartJS, 100);
        });
    </script>
</body>
</html>

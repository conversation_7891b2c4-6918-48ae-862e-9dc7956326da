import jwt
import os
from datetime import datetime, timedelta
from functools import wraps
from flask import request, jsonify, session, current_app

class TokenAuth:
    """Token认证工具类"""
    
    @staticmethod
    def generate_token(user_info, expires_hours=24):
        """生成JWT token"""
        try:
            payload = {
                'user_number': user_info.get('user_number') or user_info.get('student_number'),
                'user_name': user_info.get('user_name') or user_info.get('name'),
                'henau_openid': user_info.get('henau_openid'),
                'exp': datetime.utcnow() + timedelta(hours=expires_hours),
                'iat': datetime.utcnow()
            }
            
            secret_key = current_app.config.get('SECRET_KEY', 'default_secret')
            token = jwt.encode(payload, secret_key, algorithm='HS256')
            return token
        except Exception as e:
            print(f"Token generation error: {e}")
            return None
    
    @staticmethod
    def verify_token(token):
        """验证JWT token"""
        try:
            secret_key = current_app.config.get('SECRET_KEY', 'default_secret')
            payload = jwt.decode(token, secret_key, algorithms=['HS256'])
            return payload
        except jwt.ExpiredSignatureError:
            return None  # Token已过期
        except jwt.InvalidTokenError:
            return None  # Token无效
    
    @staticmethod
    def get_token_from_request():
        """从请求中获取token"""
        # 1. 从Authorization header获取
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            return auth_header.split(' ')[1]
        
        # 2. 从URL参数获取
        token = request.args.get('token')
        if token:
            return token
        
        # 3. 从Cookie获取
        token = request.cookies.get('auth_token')
        if token:
            return token
        
        return None

def require_auth(f):
    """装饰器：要求用户认证"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 首先检查session（向后兼容）
        if session.get('user_info'):
            return f(*args, **kwargs)
        
        # 然后检查token
        token = TokenAuth.get_token_from_request()
        if not token:
            return jsonify({'status': 'error', 'message': '未提供认证token'}), 401
        
        payload = TokenAuth.verify_token(token)
        if not payload:
            return jsonify({'status': 'error', 'message': 'Token无效或已过期'}), 401
        
        # 将用户信息添加到request context
        request.user_info = payload
        return f(*args, **kwargs)
    
    return decorated_function

def require_auth_page(f):
    """装饰器：要求页面访问认证（重定向到登录页）"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        from flask import redirect, url_for
        
        # 首先检查session
        if session.get('user_info'):
            return f(*args, **kwargs)
        
        # 然后检查token
        token = TokenAuth.get_token_from_request()
        if token:
            payload = TokenAuth.verify_token(token)
            if payload:
                return f(*args, **kwargs)
        
        # 未认证，重定向到登录页
        return redirect(url_for('login_page'))
    
    return decorated_function

<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理控制台 - 河南农业大学智能翻译平台</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="HenauLogo.png">
    <link rel="shortcut icon" type="image/png" href="HenauLogo.png">
    <link rel="apple-touch-icon" href="HenauLogo.png">
    <!-- 本地资源优先，CDN作为备选 -->
    <link href="fallback.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="tailwind.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="vue.js" onerror="this.onerror=null; this.src='https://unpkg.com/vue@3/dist/vue.global.js'"></script>
    <script src="chart.js" onerror="this.onerror=null; this.src='https://cdn.jsdelivr.net/npm/chart.js'"></script>


    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        [v-cloak] {
            display: none;
        }

        .glass-effect {
            backdrop-filter: blur(16px) saturate(180%);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .transition-all {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .sidebar-item {
            @apply flex items-center px-4 py-3 transition-all cursor-pointer;
            transition: all 0.3s ease;
            margin: 8px 12px;
            padding: 14px 18px;
            border-radius: 16px;
            font-weight: 500;
            font-size: 15px;
            color: #374151;
        }
        .dark .sidebar-item {
            color: #d1d5db;
        }

        .sidebar-item:hover {
            background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
            color: white !important;
            transform: translateX(6px) scale(1.01);
            box-shadow: 0 4px 16px rgba(96, 165, 250, 0.25);
        }

        .sidebar-item.active {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
            color: white !important;
            transform: translateX(4px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.15);
        }

        .sidebar-item.active:hover {
            transform: translateX(8px) scale(1.02);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .sidebar-item i {
            width: 20px;
            text-align: center;
            margin-right: 12px;
        }

        /* 护眼配色方案 - 支持暗黑模式 */
        .main-bg {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        .dark .main-bg {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        }

        .content-bg {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        .dark .content-bg {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        }

        .sidebar-bg {
            background: linear-gradient(180deg, #e3f2fd 0%, #bbdefb 100%);
        }
        .dark .sidebar-bg {
            background: linear-gradient(180deg, #1e40af 0%, #1e3a8a 100%);
        }

        .header-bg {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        .dark .header-bg {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        }

        /* 表格样式改进 - 支持暗黑模式 */
        .admin-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border-collapse: separate;
            border-spacing: 0;
            width: 100%;
            border: 1px solid #e5e7eb;
            table-layout: auto; /* 允许列宽自动调整 */
        }
        .dark .admin-table {
            background: #374151;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            border: 1px solid #4b5563;
        }

        .admin-table th {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            color: white;
            font-weight: 600;
            padding: 16px 12px;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            text-align: left;
            position: relative;
            user-select: none;
            min-width: 100px;
        }

        /* 列宽调整器 */
        .admin-table th .column-resizer {
            position: absolute;
            top: 0;
            right: 0;
            width: 4px;
            height: 100%;
            background: transparent;
            cursor: col-resize;
            z-index: 10;
            transition: background-color 0.2s;
        }

        .admin-table th .column-resizer:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .admin-table th .column-resizer.resizing {
            background: rgba(255, 255, 255, 0.5);
        }

        /* 移动端侧边栏样式 */
        .mobile-sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 40;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .mobile-sidebar-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .mobile-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            width: 280px;
            background: linear-gradient(180deg, #e3f2fd 0%, #bbdefb 100%);
            z-index: 50;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            overflow-y: auto;
        }

        .dark .mobile-sidebar {
            background: linear-gradient(180deg, #1e40af 0%, #1e3a8a 100%);
        }

        .mobile-sidebar.active {
            transform: translateX(0);
        }

        /* 移动端汉堡菜单 */
        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .hamburger:hover {
            background: rgba(59, 130, 246, 0.1);
        }

        .hamburger span {
            width: 24px;
            height: 3px;
            background: #3b82f6;
            margin: 2px 0;
            transition: 0.3s;
            border-radius: 2px;
        }

        .dark .hamburger span {
            background: #60a5fa;
        }

        @media (max-width: 768px) {
            .hamburger {
                display: flex;
            }
        }
        .dark .admin-table th {
            background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
        }

        .admin-table th:last-child {
            border-right: none;
        }

        .admin-table td {
            padding: 14px 12px;
            border-bottom: 2px solid #f3f4f6;
            border-right: 1px solid #e5e7eb;
            vertical-align: middle;
            color: #374151;
        }
        .dark .admin-table td {
            border-bottom: 2px solid #4b5563;
            border-right: 1px solid #6b7280;
            color: #d1d5db;
        }

        .admin-table td:last-child {
            border-right: none;
        }

        .admin-table tr:hover {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }
        .dark .admin-table tr:hover {
            background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
        }

        .admin-table tr:last-child td {
            border-bottom: none;
        }

        /* 文本单元格样式 */
        .admin-table td.text-cell-container {
            padding: 0 !important;
            vertical-align: top;
            position: relative;
            max-width: 300px; /* 设置最大宽度 */
            min-width: 150px; /* 设置最小宽度 */
        }

        .text-cell {
            position: relative;
            width: 100%;
            height: auto;
            min-height: 60px;
            max-height: 200px; /* 增加最大高度限制 */
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .text-content {
            width: 100%;
            height: auto;
            min-height: 60px;
            max-height: 200px;
            overflow-y: auto;
            line-height: 1.6; /* 增加行高提高可读性 */
            word-break: break-word;
            padding: 12px;
            margin: 0;
            border: none;
            background: transparent;
            box-sizing: border-box;
            scrollbar-width: thin;
            scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
            white-space: pre-wrap; /* 保持换行和空格 */
        }

        .text-content::-webkit-scrollbar {
            width: 4px;
        }

        .text-content::-webkit-scrollbar-track {
            background: transparent;
        }

        .text-content::-webkit-scrollbar-thumb {
            background: rgba(156, 163, 175, 0.3);
            border-radius: 2px;
        }

        .text-content::-webkit-scrollbar-thumb:hover {
            background: rgba(156, 163, 175, 0.5);
        }

        /* 鼠标悬停时显示滚动条 */
        .text-cell:hover .text-content {
            scrollbar-width: thin;
            scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
        }



        /* 表格行悬停效果优化 */
        .admin-table tbody tr:hover {
            background: rgba(59, 130, 246, 0.02);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .dark .admin-table tbody tr:hover {
            background: rgba(75, 85, 99, 0.1);
        }

        /* 文本内容优化 */
        .text-content-inner {
            line-height: 1.6;
            font-size: 14px;
        }

        /* 表格响应式优化 */
        @media (max-width: 768px) {
            .admin-table td.text-cell-container {
                max-width: 200px;
                min-width: 120px;
            }

            .text-content {
                font-size: 12px;
                padding: 8px;
            }
        }

        /* 原文和译文列的色差区分 */
        .source-text-cell {
            background: rgba(59, 130, 246, 0.05) !important; /* 淡蓝色背景 */
        }
        .dark .source-text-cell {
            background: rgba(59, 130, 246, 0.1) !important; /* 深色模式下稍深一点的蓝色 */
        }

        .target-text-cell {
            background: rgba(16, 185, 129, 0.05) !important; /* 淡绿色背景 */
        }
        .dark .target-text-cell {
            background: rgba(16, 185, 129, 0.1) !important; /* 深色模式下稍深一点的绿色 */
        }

        /* 悬停效果 */
        .source-text-cell:hover {
            background: rgba(59, 130, 246, 0.08) !important;
        }
        .dark .source-text-cell:hover {
            background: rgba(59, 130, 246, 0.15) !important;
        }

        .target-text-cell:hover {
            background: rgba(16, 185, 129, 0.08) !important;
        }
        .dark .target-text-cell:hover {
            background: rgba(16, 185, 129, 0.15) !important;
        }

        .text-cell:hover .text-content::-webkit-scrollbar {
            display: block;
            width: 4px;
        }

        .text-cell:hover .text-content::-webkit-scrollbar-track {
            background: transparent;
        }

        .text-cell:hover .text-content::-webkit-scrollbar-thumb {
            background: rgba(156, 163, 175, 0.3);
            border-radius: 2px;
        }

        .text-cell:hover .text-content::-webkit-scrollbar-thumb:hover {
            background: rgba(156, 163, 175, 0.5);
        }

        .dark .text-cell:hover .text-content::-webkit-scrollbar-thumb {
            background: rgba(75, 85, 99, 0.3);
        }

        .dark .text-cell:hover .text-content::-webkit-scrollbar-thumb:hover {
            background: rgba(75, 85, 99, 0.5);
        }

        /* 拖拽调整手柄 */
        .resize-handle {
            position: absolute;
            top: 0;
            right: 0;
            width: 8px;
            height: 100%;
            cursor: col-resize;
            background: transparent;
            z-index: 10;
            transition: background 0.2s ease;
        }

        .resize-handle:hover {
            background: linear-gradient(to right, transparent, rgba(59, 130, 246, 0.2));
        }

        .dark .resize-handle:hover {
            background: linear-gradient(to right, transparent, rgba(75, 85, 99, 0.3));
        }

        /* 文本自适应效果 */
        .text-content {
            display: flex;
            align-items: flex-start;
            text-align: left;
        }

        .text-content-inner {
            width: 100%;
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
        }

        /* 按钮样式改进 */
        .btn-primary {
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
            border: none;
            transition: all 0.3s ease;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(33, 150, 243, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .btn-danger:hover {
            background: linear-gradient(135deg, #d32f2f 0%, #c62828 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
        }

        .card {
            @apply rounded-lg shadow-sm p-6;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .dark .card {
            background: rgba(55, 65, 81, 0.9);
            border: 1px solid rgba(75, 85, 99, 0.3);
        }

        .stat-card {
            @apply bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow;
        }

        .btn-secondary {
            @apply bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors font-medium;
        }

        .btn-success {
            @apply bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors font-medium;
        }

        .table-row {
            @apply border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors;
        }

        .badge {
            @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
        }

        .badge-success {
            @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
        }

        .badge-warning {
            @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300;
        }

        .badge-danger {
            @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300;
        }

        .badge-info {
            @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300;
        }

        /* 输入框和选择框样式统一 */
        input[type="text"], input[type="email"], input[type="password"], select, textarea {
            border-radius: 12px !important;
            transition: all 0.2s ease;
            border: 1px solid #e5e7eb;
        }

        .dark input[type="text"], .dark input[type="email"], .dark input[type="password"], .dark select, .dark textarea {
            border-color: #4b5563;
        }

        input[type="text"]:focus, input[type="email"]:focus, input[type="password"]:focus, select:focus, textarea:focus {
            border-radius: 12px !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
        }

        /* 按钮圆角统一 */
        .btn-primary, .btn-secondary, .btn-danger, .btn-success {
            border-radius: 12px !important;
        }
    </style>
</head>

<body class="main-bg">
    <div id="app" v-cloak class="min-h-screen">
        <!-- Mobile Sidebar Overlay -->
        <div class="mobile-sidebar-overlay md:hidden" :class="{ active: mobileMenuOpen }" @click="mobileMenuOpen = false"></div>

        <!-- Mobile Sidebar -->
        <div class="mobile-sidebar md:hidden" :class="{ active: mobileMenuOpen }">
            <div class="flex flex-col h-full">
                <!-- Mobile Logo -->
                <div class="flex items-center justify-between h-16 px-4 border-b border-blue-200 dark:border-blue-700">
                    <h1 class="text-lg font-bold text-blue-900 dark:text-blue-100">管理控制台</h1>
                    <button @click="mobileMenuOpen = false" class="p-2 text-blue-900 dark:text-blue-100 hover:bg-blue-200 dark:hover:bg-blue-800 rounded-lg">
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>

                <!-- Mobile Navigation -->
                <nav class="flex-1 px-4 py-6 space-y-2">
                    <a @click="currentView = 'dashboard'; mobileMenuOpen = false"
                       class="flex items-center px-4 py-3 text-blue-900 dark:text-blue-100 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors cursor-pointer"
                       :class="{ 'bg-blue-200 dark:bg-blue-800': currentView === 'dashboard' }">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        <span class="font-medium">仪表盘</span>
                    </a>
                    <a @click="currentView = 'users'; mobileMenuOpen = false"
                       class="flex items-center px-4 py-3 text-blue-900 dark:text-blue-100 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors cursor-pointer"
                       :class="{ 'bg-blue-200 dark:bg-blue-800': currentView === 'users' }">
                        <i class="fas fa-users mr-3"></i>
                        <span class="font-medium">用户管理</span>
                    </a>
                    <a @click="currentView = 'blacklist'; mobileMenuOpen = false"
                       class="flex items-center px-4 py-3 text-blue-900 dark:text-blue-100 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors cursor-pointer"
                       :class="{ 'bg-blue-200 dark:bg-blue-800': currentView === 'blacklist' }">
                        <i class="fas fa-ban mr-3"></i>
                        <span class="font-medium">黑名单管理</span>
                    </a>
                    <a @click="currentView = 'translations'; mobileMenuOpen = false"
                       class="flex items-center px-4 py-3 text-blue-900 dark:text-blue-100 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors cursor-pointer"
                       :class="{ 'bg-blue-200 dark:bg-blue-800': currentView === 'translations' }">
                        <i class="fas fa-language mr-3"></i>
                        <span class="font-medium">翻译记录</span>
                    </a>
                    <a @click="currentView = 'analytics'; mobileMenuOpen = false"
                       class="flex items-center px-4 py-3 text-blue-900 dark:text-blue-100 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors cursor-pointer"
                       :class="{ 'bg-blue-200 dark:bg-blue-800': currentView === 'analytics' }">
                        <i class="fas fa-chart-bar mr-3"></i>
                        <span class="font-medium">数据分析</span>
                    </a>
                    <a @click="currentView = 'logs'; mobileMenuOpen = false"
                       class="flex items-center px-4 py-3 text-blue-900 dark:text-blue-100 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors cursor-pointer"
                       :class="{ 'bg-blue-200 dark:bg-blue-800': currentView === 'logs' }">
                        <i class="fas fa-clipboard-list mr-3"></i>
                        <span class="font-medium">操作日志</span>
                    </a>
                </nav>
            </div>
        </div>

        <!-- Header -->
        <header class="header-bg shadow-lg border-b border-blue-200 relative z-20">
            <div class="px-4 md:px-6 py-4">
                <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-4">
                        <!-- Mobile Menu Button -->
                        <button @click="mobileMenuOpen = true" class="hamburger md:hidden">
                            <span></span>
                            <span></span>
                            <span></span>
                        </button>
                        <h1 class="text-lg md:text-xl font-semibold text-gray-900 dark:text-white">管理控制台</h1>
                        <span class="hidden sm:block text-sm text-gray-500 dark:text-gray-400">河南农业大学智能翻译平台</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <!-- Dark Mode Toggle -->
                        <button @click="toggleDarkMode"
                            class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 transition-colors">
                            <i :class="isDarkMode ? 'fas fa-sun' : 'fas fa-moon'"></i>
                        </button>
                        <!-- User Info -->
                        <div class="flex items-center space-x-3">
                            <div class="text-right">
                                <div class="text-sm font-medium text-gray-900 dark:text-white">{{ userInfo.username }}</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">{{ getRoleText(userInfo.role) }}</div>
                            </div>
                            <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                        </div>
                        <!-- Back to Main -->
                        <a href="/" class="btn-secondary">
                            <i class="fas fa-arrow-left mr-2"></i>返回主页
                        </a>
                    </div>
                </div>
            </div>
        </header>

        <div class="flex">
            <!-- Desktop Sidebar -->
            <aside class="hidden md:block w-64 sidebar-bg shadow-lg border-r border-blue-200 min-h-screen">
                <nav class="p-4 space-y-2">
                    <div @click="currentView = 'dashboard'" class="sidebar-item" :class="{ active: currentView === 'dashboard' }">
                        <i class="fas fa-chart-line mr-3"></i>
                        <span>仪表板</span>
                    </div>
                    <div @click="currentView = 'users'" class="sidebar-item" :class="{ active: currentView === 'users' }">
                        <i class="fas fa-users mr-3"></i>
                        <span>用户管理</span>
                    </div>
                    <div @click="currentView = 'blacklist'" class="sidebar-item" :class="{ active: currentView === 'blacklist' }">
                        <i class="fas fa-ban mr-3"></i>
                        <span>黑名单管理</span>
                    </div>
                    <div @click="currentView = 'translations'" class="sidebar-item" :class="{ active: currentView === 'translations' }">
                        <i class="fas fa-language mr-3"></i>
                        <span>翻译记录</span>
                    </div>
                    <div @click="currentView = 'analytics'" class="sidebar-item" :class="{ active: currentView === 'analytics' }">
                        <i class="fas fa-chart-bar mr-3"></i>
                        <span>数据分析</span>
                    </div>
                    <div @click="currentView = 'logs'" class="sidebar-item" :class="{ active: currentView === 'logs' }">
                        <i class="fas fa-clipboard-list mr-3"></i>
                        <span>操作日志</span>
                    </div>
                </nav>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 p-4 md:p-6 content-bg w-full md:w-auto">
                <!-- Dashboard View -->
                <div v-if="currentView === 'dashboard'" class="space-y-4 md:space-y-6">
                    <h2 class="text-xl md:text-2xl font-bold text-gray-900 dark:text-white">仪表板</h2>

                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
                        <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/30 rounded-2xl p-4 md:p-6 border border-blue-200 dark:border-blue-700 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-3">
                                        <div class="w-8 h-8 md:w-10 md:h-10 bg-blue-500 rounded-xl flex items-center justify-center mr-3">
                                            <i class="fas fa-users text-white text-sm md:text-lg"></i>
                                        </div>
                                        <h3 class="text-base md:text-lg font-semibold text-blue-900 dark:text-blue-100">用户总数</h3>
                                    </div>
                                    <p class="text-2xl md:text-3xl font-bold text-blue-900 dark:text-blue-100 mb-1">{{ dashboardData.user_stats?.total || 0 }}</p>
                                    <p class="text-xs md:text-sm text-blue-600 dark:text-blue-300">注册用户</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/30 rounded-2xl p-6 border border-blue-200 dark:border-blue-700 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-3">
                                        <div class="w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center mr-3">
                                            <i class="fas fa-language text-white text-lg"></i>
                                        </div>
                                        <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100">今日翻译</h3>
                                    </div>
                                    <p class="text-3xl font-bold text-blue-900 dark:text-blue-100 mb-1">{{ dashboardData.translation_stats?.today || 0 }}</p>
                                    <p class="text-sm text-blue-600 dark:text-blue-300">次数</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/30 rounded-2xl p-6 border border-blue-200 dark:border-blue-700 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-3">
                                        <div class="w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center mr-3">
                                            <i class="fas fa-chart-line text-white text-lg"></i>
                                        </div>
                                        <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100">总翻译</h3>
                                    </div>
                                    <p class="text-3xl font-bold text-blue-900 dark:text-blue-100 mb-1">{{ dashboardData.translation_stats?.total || 0 }}</p>
                                    <p class="text-sm text-blue-600 dark:text-blue-300">累计次数</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/30 dark:to-blue-800/30 rounded-2xl p-6 border border-blue-200 dark:border-blue-700 hover:shadow-lg transition-all duration-300">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center mb-3">
                                        <div class="w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center mr-3">
                                            <i class="fas fa-ban text-white text-lg"></i>
                                        </div>
                                        <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100">封禁用户</h3>
                                    </div>
                                    <p class="text-3xl font-bold text-blue-900 dark:text-blue-100 mb-1">{{ dashboardData.blacklist_count || 0 }}</p>
                                    <p class="text-sm text-blue-600 dark:text-blue-300">黑名单</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 图表区域 -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
                        <!-- 翻译趋势图 -->
                        <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-2xl p-6 border border-blue-200 dark:border-blue-700 shadow-lg hover:shadow-xl transition-all duration-300">
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center mr-3">
                                        <i class="fas fa-chart-line text-white text-lg"></i>
                                    </div>
                                    <h3 class="text-xl font-bold text-blue-900 dark:text-blue-100">翻译趋势</h3>
                                </div>
                                <div class="text-sm text-blue-600 dark:text-blue-300">近7天</div>
                            </div>
                            <div class="h-72 bg-white dark:bg-gray-800/50 rounded-xl p-4 border border-blue-100 dark:border-blue-800">
                                <div v-if="translationTrendLoading" class="h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
                                    <div class="text-center">
                                        <i class="fas fa-spinner fa-spin text-4xl mb-4 opacity-50"></i>
                                        <p>加载中...</p>
                                    </div>
                                </div>
                                <canvas v-else id="translationChart" class="w-full h-full"></canvas>
                            </div>
                        </div>

                        <!-- 语言分布图 -->
                        <div class="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-2xl p-6 border border-blue-200 dark:border-blue-700 shadow-lg hover:shadow-xl transition-all duration-300">
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center mr-3">
                                        <i class="fas fa-globe text-white text-lg"></i>
                                    </div>
                                    <h3 class="text-xl font-bold text-blue-900 dark:text-blue-100">热门语言对</h3>
                                </div>
                                <div class="text-sm text-blue-600 dark:text-blue-300">使用频率</div>
                            </div>
                            <div class="h-72 bg-white dark:bg-gray-800/50 rounded-xl p-4 border border-blue-100 dark:border-blue-800">
                                <div v-if="languagePairsLoading" class="h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
                                    <div class="text-center">
                                        <i class="fas fa-spinner fa-spin text-4xl mb-4 opacity-50"></i>
                                        <p>加载中...</p>
                                    </div>
                                </div>
                                <canvas v-else id="languageChart" class="w-full h-full"></canvas>
                            </div>
                        </div>
                    </div>

                </div>

                <!-- Users View -->
                <div v-if="currentView === 'users'" class="space-y-4 md:space-y-6">
                    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
                        <h2 class="text-xl md:text-2xl font-bold text-gray-900 dark:text-white">用户管理</h2>
                        <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
                            <button v-if="userInfo.role === 'super_admin'" @click="showAddUserModal = true" class="btn-success flex items-center justify-center">
                                <i class="fas fa-plus mr-2"></i>添加用户
                            </button>
                            <button @click="exportUsers" class="btn-secondary flex items-center justify-center">
                                <i class="fas fa-download mr-2"></i>导出用户 CSV
                            </button>
                            <button @click="loadUsers" class="btn-primary flex items-center">
                                <i class="fas fa-sync-alt mr-2"></i>刷新
                            </button>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700 shadow-sm">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-filter text-white text-sm"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100">筛选条件</h3>
                        </div>
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
                            <div>
                                <label class="block text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3">搜索用户</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-search text-blue-400 text-sm"></i>
                                    </div>
                                    <input v-model="userFilters.search" @input="loadUsers" type="text" placeholder="输入学号或姓名"
                                        class="w-full pl-10 pr-4 py-3 border border-blue-200 dark:border-blue-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white bg-white shadow-sm transition-all duration-200 hover:shadow-md">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3">角色筛选</label>
                                <div class="relative">
                                    <select v-model="userFilters.role" @change="loadUsers"
                                        class="w-full px-4 py-3 border border-blue-200 dark:border-blue-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white bg-white shadow-sm transition-all duration-200 hover:shadow-md appearance-none">
                                        <option value="">全部角色</option>
                                        <option value="user">普通用户</option>
                                        <option value="admin">管理员</option>
                                        <option value="super_admin">超级管理员</option>
                                    </select>
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-chevron-down text-blue-400 text-sm"></i>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3">每页显示</label>
                                <div class="relative">
                                    <select v-model="userFilters.per_page" @change="loadUsers"
                                        class="w-full px-4 py-3 border border-blue-200 dark:border-blue-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white bg-white shadow-sm transition-all duration-200 hover:shadow-md appearance-none">
                                        <option value="10">10 条/页</option>
                                        <option value="20">20 条/页</option>
                                        <option value="50">50 条/页</option>
                                    </select>
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-chevron-down text-blue-400 text-sm"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Users Table -->
                    <div class="card">
                        <div class="overflow-x-auto -mx-4 md:mx-0">
                            <div class="min-w-full inline-block align-middle">
                                <table class="admin-table w-full">
                                <thead>
                                    <tr class="border-b border-gray-200 dark:border-gray-700">
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                                            学号
                                            <div class="column-resizer" @mousedown="startResize($event, 0)"></div>
                                        </th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                                            姓名
                                            <div class="column-resizer" @mousedown="startResize($event, 1)"></div>
                                        </th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                                            学院
                                            <div class="column-resizer" @mousedown="startResize($event, 2)"></div>
                                        </th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                                            角色
                                            <div class="column-resizer" @mousedown="startResize($event, 3)"></div>
                                        </th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                                            状态
                                            <div class="column-resizer" @mousedown="startResize($event, 4)"></div>
                                        </th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="user in users" :key="user.id" class="table-row">
                                        <td class="py-3 px-4 text-gray-900 dark:text-white">{{ user.user_number }}</td>
                                        <td class="py-3 px-4 text-gray-900 dark:text-white">{{ user.username }}</td>
                                        <td class="py-3 px-4 text-gray-500 dark:text-gray-400">{{ user.user_section || '-' }}</td>
                                        <td class="py-3 px-4">
                                            <span class="badge" :class="getRoleBadgeClass(user.role)">
                                                {{ getRoleText(user.role) }}
                                            </span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <span class="badge" :class="user.is_blacklisted ? 'badge-danger' : 'badge-success'">
                                                {{ user.is_blacklisted ? '已封禁' : '正常' }}
                                            </span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <div class="flex space-x-2">
                                                <button v-if="!user.is_blacklisted" @click="banUser(user)" class="text-red-600 hover:text-red-800 text-sm">
                                                    <i class="fas fa-ban mr-1"></i>封禁
                                                </button>
                                                <button v-if="user.is_blacklisted" @click="unbanUser(user)" class="text-green-600 hover:text-green-800 text-sm">
                                                    <i class="fas fa-check mr-1"></i>解封
                                                </button>
                                                <button v-if="userInfo.role === 'super_admin' && user.role === 'user'" @click="grantAdmin(user)" class="text-blue-600 hover:text-blue-800 text-sm">
                                                    <i class="fas fa-user-shield mr-1"></i>授权
                                                </button>
                                                <button v-if="userInfo.role === 'super_admin' && user.role === 'admin'" @click="revokeAdmin(user)" class="text-orange-600 hover:text-orange-800 text-sm">
                                                    <i class="fas fa-user-minus mr-1"></i>撤权
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Pagination -->
                        <div v-if="userPagination.pages > 1" class="mt-4 flex flex-col sm:flex-row justify-between items-center gap-4">
                            <div class="text-sm text-gray-700 dark:text-gray-300 text-center sm:text-left">
                                显示 {{ (userPagination.page - 1) * userPagination.per_page + 1 }} -
                                {{ Math.min(userPagination.page * userPagination.per_page, userPagination.total) }}
                                共 {{ userPagination.total }} 条
                            </div>
                            <div class="flex space-x-2">
                                <button @click="loadUsers(userPagination.page - 1)" :disabled="userPagination.page <= 1"
                                    class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm disabled:opacity-50 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    上一页
                                </button>
                                <button @click="loadUsers(userPagination.page + 1)" :disabled="userPagination.page >= userPagination.pages"
                                    class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm disabled:opacity-50 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    下一页
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Blacklist View -->
                <div v-if="currentView === 'blacklist'" class="space-y-6">
                    <div class="flex justify-between items-center">
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">黑名单管理</h2>
                        <button @click="loadBlacklist" class="btn-primary">
                            <i class="fas fa-sync-alt mr-2"></i>刷新
                        </button>
                    </div>

                    <!-- Blacklist Table -->
                    <div class="card">
                        <div class="overflow-x-auto">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">学号</th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">姓名</th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">封禁原因</th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">操作者</th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">封禁时间</th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">状态</th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="entry in blacklist" :key="entry.id" class="table-row">
                                        <td class="py-3 px-4 text-gray-900 dark:text-white">{{ entry.user_number }}</td>
                                        <td class="py-3 px-4 text-gray-900 dark:text-white">{{ entry.username }}</td>
                                        <td class="py-3 px-4 text-gray-500 dark:text-gray-400">{{ entry.reason || '-' }}</td>
                                        <td class="py-3 px-4 text-gray-500 dark:text-gray-400">{{ entry.banned_by_name }}</td>
                                        <td class="py-3 px-4 text-gray-500 dark:text-gray-400">{{ formatDate(entry.banned_at) }}</td>
                                        <td class="py-3 px-4">
                                            <span class="badge" :class="entry.is_active ? 'badge-danger' : 'badge-success'">
                                                {{ entry.is_active ? '已封禁' : '已解封' }}
                                            </span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <button v-if="entry.is_active" @click="unbanUserFromBlacklist(entry)" class="text-green-600 hover:text-green-800 text-sm">
                                                <i class="fas fa-check mr-1"></i>解封
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Translations View -->
                <div v-if="currentView === 'translations'" class="space-y-6">
                    <div class="flex justify-between items-center">
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">翻译记录</h2>
                        <div class="flex flex-wrap items-center gap-3">
                            <button @click="exportTranslations" class="btn-secondary flex items-center">
                                <i class="fas fa-download mr-2"></i>导出数据 CSV
                            </button>
                            <button @click="loadTranslations" class="btn-primary flex items-center">
                                <i class="fas fa-sync-alt mr-2"></i>刷新
                            </button>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700 shadow-sm">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-filter text-white text-sm"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100">筛选条件</h3>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3">用户学号</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-user text-blue-400 text-sm"></i>
                                    </div>
                                    <input v-model="translationFilters.user_number" @input="loadTranslations" type="text" placeholder="输入学号筛选"
                                        class="w-full pl-10 pr-4 py-3 border border-blue-200 dark:border-blue-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white bg-white shadow-sm transition-all duration-200 hover:shadow-md">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3">每页显示</label>
                                <div class="relative">
                                    <select v-model="translationFilters.per_page" @change="loadTranslations"
                                        class="w-full px-4 py-3 border border-blue-200 dark:border-blue-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white bg-white shadow-sm transition-all duration-200 hover:shadow-md appearance-none">
                                        <option value="10">10 条/页</option>
                                        <option value="20">20 条/页</option>
                                        <option value="50">50 条/页</option>
                                    </select>
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-chevron-down text-blue-400 text-sm"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Translations Table -->
                    <div class="card">
                        <div class="overflow-x-auto">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                                            用户
                                            <div class="column-resizer" @mousedown="startResize($event, 0)"></div>
                                        </th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                                            原文
                                            <div class="column-resizer" @mousedown="startResize($event, 1)"></div>
                                        </th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                                            译文
                                            <div class="column-resizer" @mousedown="startResize($event, 2)"></div>
                                        </th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                                            语言对
                                            <div class="column-resizer" @mousedown="startResize($event, 3)"></div>
                                        </th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                                            模型
                                            <div class="column-resizer" @mousedown="startResize($event, 4)"></div>
                                        </th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                                            时间
                                            <div class="column-resizer" @mousedown="startResize($event, 5)"></div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="trans in translations" :key="trans.id" class="table-row">
                                        <td class="py-3 px-4 text-gray-900 dark:text-white">
                                            <div>{{ trans.user_name }}</div>
                                            <div class="text-xs text-gray-500">{{ trans.user_number }}</div>
                                        </td>
                                        <td class="text-cell-container text-gray-500 dark:text-gray-400 source-text-cell">
                                            <div class="text-cell">
                                                <div class="text-content" :title="trans.source_text">
                                                    <div class="text-content-inner">{{ trans.source_text }}</div>
                                                </div>
                                                <div class="resize-handle" @mousedown="initResize($event, `source_${trans.id}`)"></div>
                                            </div>
                                        </td>
                                        <td class="text-cell-container text-gray-500 dark:text-gray-400 target-text-cell">
                                            <div class="text-cell">
                                                <div class="text-content" :title="trans.translated_text">
                                                    <div class="text-content-inner">{{ trans.translated_text }}</div>
                                                </div>
                                                <div class="resize-handle" @mousedown="initResize($event, `target_${trans.id}`)"></div>
                                            </div>
                                        </td>
                                        <td class="py-3 px-4 text-gray-500 dark:text-gray-400">{{ trans.source_language || 'N/A' }} → {{ trans.target_language || 'N/A' }}</td>
                                        <td class="py-3 px-4 text-gray-500 dark:text-gray-400">{{ trans.model_used }}</td>
                                        <td class="py-3 px-4 text-gray-500 dark:text-gray-400">{{ formatDate(trans.created_at) }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination for Translations -->
                        <div v-if="translationPagination.pages > 1" class="mt-6 flex flex-col sm:flex-row justify-between items-center gap-4 px-6 pb-6">
                            <div class="text-sm text-gray-700 dark:text-gray-300 text-center sm:text-left">
                                显示 {{ (translationPagination.page - 1) * translationPagination.per_page + 1 }} -
                                {{ Math.min(translationPagination.page * translationPagination.per_page, translationPagination.total) }}
                                共 {{ translationPagination.total }} 条翻译记录
                            </div>
                            <div class="flex items-center space-x-2">
                                <button @click="loadTranslations(1)" :disabled="translationPagination.page <= 1"
                                    class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm disabled:opacity-50 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    首页
                                </button>
                                <button @click="loadTranslations(translationPagination.page - 1)" :disabled="translationPagination.page <= 1"
                                    class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm disabled:opacity-50 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    上一页
                                </button>
                                <span class="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg">
                                    第 {{ translationPagination.page }} / {{ translationPagination.pages }} 页
                                </span>
                                <button @click="loadTranslations(translationPagination.page + 1)" :disabled="translationPagination.page >= translationPagination.pages"
                                    class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm disabled:opacity-50 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    下一页
                                </button>
                                <button @click="loadTranslations(translationPagination.pages)" :disabled="translationPagination.page >= translationPagination.pages"
                                    class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm disabled:opacity-50 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                    末页
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analytics View -->
                <div v-if="currentView === 'analytics'" class="space-y-6">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white">数据分析</h2>

                    <!-- User Analytics -->
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl p-6 border border-blue-200 dark:border-blue-700 shadow-lg">
                        <div class="flex items-center mb-6">
                            <div class="w-10 h-10 bg-blue-500 rounded-xl flex items-center justify-center mr-3">
                                <i class="fas fa-users text-white text-lg"></i>
                            </div>
                            <h3 class="text-xl font-bold text-blue-900 dark:text-blue-100">用户活跃度分析</h3>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                            <div class="bg-white dark:bg-gray-800/50 rounded-xl p-4 border border-blue-100 dark:border-blue-800 text-center">
                                <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">今日活跃</p>
                                <p class="text-3xl font-bold text-green-600 dark:text-green-400">{{ userAnalytics.today_active || 0 }}</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">今日登录用户数</p>
                            </div>
                            <div class="bg-white dark:bg-gray-800/50 rounded-xl p-4 border border-blue-100 dark:border-blue-800 text-center">
                                <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">本周活跃</p>
                                <p class="text-3xl font-bold text-green-600 dark:text-green-400">{{ userAnalytics.week_active || 0 }}</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">近7天登录用户数</p>
                            </div>
                            <div class="bg-white dark:bg-gray-800/50 rounded-xl p-4 border border-blue-100 dark:border-blue-800 text-center">
                                <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">月活跃用户</p>
                                <p class="text-3xl font-bold text-green-600 dark:text-green-400">{{ userAnalytics.monthly_active || 0 }}</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">近30天登录用户数</p>
                            </div>
                            <div class="bg-white dark:bg-gray-800/50 rounded-xl p-4 border border-blue-100 dark:border-blue-800 text-center">
                                <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">年活跃用户</p>
                                <p class="text-3xl font-bold text-green-600 dark:text-green-400">{{ userAnalytics.yearly_active || 0 }}</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">近365天登录用户数</p>
                            </div>
                        </div>

                        <!-- Daily Active Table -->
                        <div class="bg-white dark:bg-gray-800/50 rounded-xl border border-blue-100 dark:border-blue-800 overflow-hidden">
                            <div class="px-6 py-4 bg-blue-100 dark:bg-blue-900/30 border-b border-blue-200 dark:border-blue-700">
                                <h4 class="font-semibold text-blue-900 dark:text-blue-100">最近7天日活跃用户</h4>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="w-full">
                                    <thead class="bg-gray-50 dark:bg-gray-700/50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">日期</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">活跃用户数</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                                        <tr v-for="day in userAnalytics.daily_active" :key="day.date" class="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ formatDateOnly(day.date) }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600 dark:text-blue-400">{{ day.count }} 人</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Translation Analytics -->
                    <div class="bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20 rounded-2xl p-6 border border-green-200 dark:border-green-700 shadow-lg">
                        <div class="flex items-center mb-6">
                            <div class="w-10 h-10 bg-green-500 rounded-xl flex items-center justify-center mr-3">
                                <i class="fas fa-language text-white text-lg"></i>
                            </div>
                            <h3 class="text-xl font-bold text-green-900 dark:text-green-100">翻译数据分析</h3>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Language Pairs -->
                            <div class="bg-white dark:bg-gray-800/50 rounded-xl border border-green-100 dark:border-green-800 overflow-hidden">
                                <div class="px-6 py-4 bg-green-100 dark:bg-green-900/30 border-b border-green-200 dark:border-green-700">
                                    <h4 class="font-semibold text-green-900 dark:text-green-100">热门语言对</h4>
                                </div>
                                <div class="overflow-x-auto">
                                    <table class="w-full">
                                        <thead class="bg-gray-50 dark:bg-gray-700/50">
                                            <tr>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">语言对</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">使用次数</th>
                                            </tr>
                                        </thead>
                                        <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                                            <tr v-for="pair in translationAnalytics.language_pairs" :key="`${pair.source_lang}-${pair.target_lang}`" class="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                                    <span class="inline-flex items-center">
                                                        {{ pair.source_lang }}
                                                        <i class="fas fa-arrow-right mx-2 text-gray-400"></i>
                                                        {{ pair.target_lang }}
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600 dark:text-green-400">{{ pair.count }} 次</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Model Usage -->
                            <div class="bg-white dark:bg-gray-800/50 rounded-xl border border-green-100 dark:border-green-800 overflow-hidden">
                                <div class="px-6 py-4 bg-green-100 dark:bg-green-900/30 border-b border-green-200 dark:border-green-700">
                                    <h4 class="font-semibold text-green-900 dark:text-green-100">模型使用统计</h4>
                                </div>
                                <div class="overflow-x-auto">
                                    <table class="w-full">
                                        <thead class="bg-gray-50 dark:bg-gray-700/50">
                                            <tr>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">模型名称</th>
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">使用次数</th>
                                            </tr>
                                        </thead>
                                        <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                                            <tr v-for="model in translationAnalytics.model_usage" :key="model.model" class="hover:bg-gray-50 dark:hover:bg-gray-700/50">
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ model.model }}</td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600 dark:text-green-400">{{ model.count }} 次</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Logs View -->
                <div v-if="currentView === 'logs'" class="space-y-6">
                    <div class="flex justify-between items-center">
                        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">操作日志</h2>
                        <div class="flex space-x-3">
                            <button @click="exportLogs" class="btn-secondary">
                                <i class="fas fa-download mr-2"></i>导出日志 CSV
                            </button>
                            <button @click="loadLogs" class="btn-primary">
                                <i class="fas fa-sync-alt mr-2"></i>刷新
                            </button>
                        </div>
                    </div>

                    <!-- Log Filters -->
                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700 shadow-sm">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-filter text-white text-sm"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100">筛选条件</h3>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label class="block text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3">操作类型</label>
                                <div class="relative">
                                    <select v-model="logFilters.action" @change="loadLogs"
                                        class="w-full px-4 py-3 border border-blue-200 dark:border-blue-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white bg-white shadow-sm transition-all duration-200 hover:shadow-md appearance-none">
                                        <option value="">全部操作</option>
                                        <option v-for="action in availableActions" :key="action" :value="action">{{ getActionText(action) }}</option>
                                    </select>
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-chevron-down text-blue-400 text-sm"></i>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3">操作者学号</label>
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <i class="fas fa-user-shield text-blue-400 text-sm"></i>
                                    </div>
                                    <input v-model="logFilters.operator" @input="loadLogs" type="text" placeholder="输入操作者学号"
                                        class="w-full pl-10 pr-4 py-3 border border-blue-200 dark:border-blue-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white bg-white shadow-sm transition-all duration-200 hover:shadow-md">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3">每页显示</label>
                                <div class="relative">
                                    <select v-model="logFilters.per_page" @change="loadLogs"
                                        class="w-full px-4 py-3 border border-blue-200 dark:border-blue-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white bg-white shadow-sm transition-all duration-200 hover:shadow-md appearance-none">
                                        <option value="10">10 条/页</option>
                                        <option value="20">20 条/页</option>
                                        <option value="50">50 条/页</option>
                                    </select>
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <i class="fas fa-chevron-down text-blue-400 text-sm"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Logs Table -->
                    <div class="card">
                        <div class="overflow-x-auto">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">时间                                        </th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">                                            操作者                                        </th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">操作                                        </th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">描述                                        </th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">                                            目标用户                                        </th>
                                        <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="log in logs" :key="log.id" class="table-row">
                                        <td class="py-3 px-4 text-gray-500 dark:text-gray-400 text-sm">{{ formatDate(log.created_at) }}</td>
                                        <td class="py-3 px-4 text-gray-900 dark:text-white">
                                            <div>{{ log.operator_username }}</div>
                                            <div class="text-xs text-gray-500">{{ log.operator_user_number }}</div>
                                        </td>
                                        <td class="py-3 px-4">
                                            <span class="badge badge-info">{{ getActionText(log.action) }}</span>
                                        </td>
                                        <td class="py-3 px-4 text-gray-500 dark:text-gray-400 max-w-xs truncate">{{ log.description }}</td>
                                        <td class="py-3 px-4 text-gray-500 dark:text-gray-400">
                                            <div v-if="log.target_username">
                                                <div>{{ log.target_username }}</div>
                                                <div v-if="log.target_user_number" class="text-xs">{{ log.target_user_number }}</div>
                                            </div>
                                            <span v-else class="text-gray-400">-</span>
                                        </td>
                                        <td class="py-3 px-4">
                                            <span class="badge" :class="log.status === 'success' ? 'badge-success' : 'badge-danger'">
                                                {{ log.status === 'success' ? '成功' : '失败' }}
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div v-if="logsPagination.pages > 1" class="mt-4 flex justify-between items-center">
                            <div class="text-sm text-gray-700 dark:text-gray-300">
                                显示 {{ (logsPagination.page - 1) * logsPagination.per_page + 1 }} -
                                {{ Math.min(logsPagination.page * logsPagination.per_page, logsPagination.total) }}
                                共 {{ logsPagination.total }} 条
                            </div>
                            <div class="flex space-x-2">
                                <button @click="loadLogs(logsPagination.page - 1)" :disabled="logsPagination.page <= 1"
                                    class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm disabled:opacity-50 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    上一页
                                </button>
                                <span class="px-3 py-1 text-sm text-gray-700 dark:text-gray-300">
                                    第 {{ logsPagination.page }} / {{ logsPagination.pages }} 页
                                </span>
                                <button @click="loadLogs(logsPagination.page + 1)" :disabled="logsPagination.page >= logsPagination.pages"
                                    class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm disabled:opacity-50 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    下一页
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loading State -->
                <div v-if="loading" class="flex justify-center items-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                </div>
            </main>
        </div>

        <!-- Add User Modal -->
        <div v-if="showAddUserModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 md:p-6 w-full max-w-md max-h-screen overflow-y-auto">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">添加用户</h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">学号</label>
                        <input v-model="newUser.user_number" type="text" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" placeholder="请输入学号">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">姓名</label>
                        <input v-model="newUser.username" type="text" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" placeholder="请输入姓名">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">角色</label>
                        <select v-model="newUser.role" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            <option value="user">普通用户</option>
                            <option value="admin">管理员</option>
                            <option value="super_admin">超级管理员</option>
                        </select>
                    </div>

                </div>
                <div class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3 mt-6">
                    <button @click="showAddUserModal = false; resetNewUser()" class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg">
                        取消
                    </button>
                    <button @click="addUser" class="btn-success" :disabled="!newUser.user_number || !newUser.username">
                        添加用户
                    </button>
                </div>
            </div>
        </div>

        <!-- Ban User Modal -->
        <div v-if="showBanModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">封禁用户</h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">用户信息</label>
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ selectedUser?.username }} ({{ selectedUser?.user_number }})</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">封禁原因</label>
                        <textarea v-model="banReason" placeholder="请输入封禁原因..."
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                            rows="3"></textarea>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button @click="showBanModal = false" class="btn-secondary">取消</button>
                    <button @click="confirmBan" class="btn-danger">确认封禁</button>
                </div>
            </div>
        </div>
    </div>



    <!-- Vue.js Application -->
    <script>
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    // UI State
                    isDarkMode: localStorage.getItem('darkMode') === 'true',
                    currentView: 'dashboard',
                    loading: false,
                    showAddUserModal: false,
                    mobileMenuOpen: false,

                    // Auth State
                    userInfo: {},
                    isAuthenticated: false,

                    // Dashboard Data
                    dashboardData: {},
                    translationTrendLoading: false,
                    languagePairsLoading: false,
                    translationChart: null,
                    languageChart: null,

                    // Users Management
                    users: [],
                    userPagination: {},
                    userFilters: {
                        search: '',
                        role: '',
                        per_page: 20
                    },
                    newUser: {
                        user_number: '',
                        username: '',
                        role: 'user'
                    },

                    // Blacklist Management
                    blacklist: [],
                    blacklistPagination: {},

                    // Translations Management
                    translations: [],
                    translationPagination: {},
                    translationFilters: {
                        user_number: '',
                        per_page: 20
                    },


                    // Analytics Data
                    userAnalytics: {},
                    translationAnalytics: {},

                    // Logs Management
                    logs: [],
                    logsPagination: {},
                    logFilters: {
                        action: '',
                        operator: '',
                        per_page: 20
                    },
                    availableActions: [],

                    // Modals
                    showBanModal: false,
                    selectedUser: null,
                    banReason: '',
                }
            },

            async mounted() {
                try {
                    await this.checkAuth();
                    this.applyDarkMode();
                    await this.loadDashboard();
                    // 延迟初始化图表，确保DOM已渲染
                    setTimeout(() => {
                        this.loadChartData();
                    }, 2000);
                } catch (error) {
                    console.error('初始化错误:', error);
                }
            },

            methods: {
                // Dark Mode
                toggleDarkMode() {
                    this.isDarkMode = !this.isDarkMode;
                    localStorage.setItem('darkMode', this.isDarkMode);
                    this.applyDarkMode();
                    // 重新渲染图表以适应主题变化
                    setTimeout(() => {
                        this.loadChartData();
                    }, 100);
                },

                applyDarkMode() {
                    if (this.isDarkMode) {
                        document.documentElement.classList.add('dark');
                    } else {
                        document.documentElement.classList.remove('dark');
                    }
                },

                // 加载图表数据
                async loadChartData() {
                    console.log('loadChartData called');
                    console.log('Chart.js available:', typeof Chart !== 'undefined');

                    // 直接尝试加载图表数据，不依赖标志
                    try {
                        await Promise.all([
                            this.loadTranslationTrend(),
                            this.loadLanguagePairs()
                        ]);
                    } catch (error) {
                        console.error('Failed to load chart data:', error);
                    }
                },

                async loadTranslationTrend() {
                    try {
                        console.log('Loading translation trend...');
                        this.translationTrendLoading = true;
                        const response = await fetch('/admin/dashboard/translation-trend', {
                            credentials: 'include',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });
                        const data = await response.json();
                        console.log('Translation trend response:', data);

                        if (data.status === 'success') {
                            console.log('Initializing translation chart with data:', data.data);
                            this.initTranslationChart(data.data);
                        } else {
                            console.error('Translation trend API error:', data.message);
                            // 使用默认数据
                            this.initTranslationChart(this.getDefaultTrendData());
                        }
                    } catch (error) {
                        console.error('加载翻译趋势数据失败:', error);
                        // 使用默认数据
                        this.initTranslationChart(this.getDefaultTrendData());
                    } finally {
                        this.translationTrendLoading = false;
                    }
                },

                async loadLanguagePairs() {
                    try {
                        console.log('Loading language pairs...');
                        this.languagePairsLoading = true;
                        const response = await fetch('/admin/dashboard/language-pairs', {
                            credentials: 'include',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });
                        const data = await response.json();
                        console.log('Language pairs response:', data);

                        if (data.status === 'success') {
                            console.log('Initializing language chart with data:', data.data);
                            this.initLanguageChart(data.data);
                        } else {
                            console.error('Language pairs API error:', data.message);
                            // 使用默认数据
                            this.initLanguageChart(this.getDefaultLanguageData());
                        }
                    } catch (error) {
                        console.error('加载语言对数据失败:', error);
                        // 使用默认数据
                        this.initLanguageChart(this.getDefaultLanguageData());
                    } finally {
                        this.languagePairsLoading = false;
                    }
                },

                initTranslationChart(chartData) {
                    console.log('initTranslationChart called with data:', chartData);
                    const ctx = document.getElementById('translationChart');

                    if (!ctx) {
                        console.error('Canvas element not found');
                        return;
                    }

                    if (typeof Chart === 'undefined') {
                        console.error('Chart.js not loaded');
                        return;
                    }

                    // 销毁现有图表
                    if (this.translationChart) {
                        try {
                            this.translationChart.destroy();
                        } catch (e) {
                            console.warn('Error destroying existing chart:', e);
                        }
                    }

                    const isDark = this.isDarkMode;
                    const textColor = isDark ? '#d1d5db' : '#374151';
                    const gridColor = isDark ? '#4b5563' : '#e5e7eb';

                    try {
                        this.translationChart = new Chart(ctx, {
                            type: 'line',
                            data: chartData,
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        labels: {
                                            color: textColor
                                        }
                                    }
                                },
                                scales: {
                                    y: {
                                        beginAtZero: true,
                                        ticks: {
                                            color: textColor
                                        },
                                        grid: {
                                            color: gridColor
                                        }
                                    },
                                    x: {
                                        ticks: {
                                            color: textColor
                                        },
                                        grid: {
                                            color: gridColor
                                        }
                                    }
                                }
                            }
                        });

                        console.log('Translation chart created successfully');
                    } catch (error) {
                        console.error('Error creating translation chart:', error);
                    }
                },

                initLanguageChart(chartData) {
                    console.log('initLanguageChart called with data:', chartData);
                    const ctx = document.getElementById('languageChart');

                    if (!ctx) {
                        console.error('Language chart canvas element not found');
                        return;
                    }

                    if (typeof Chart === 'undefined') {
                        console.error('Chart.js not loaded for language chart');
                        return;
                    }

                    // 销毁现有图表
                    if (this.languageChart) {
                        try {
                            this.languageChart.destroy();
                        } catch (e) {
                            console.warn('Error destroying existing language chart:', e);
                        }
                    }

                    const isDark = this.isDarkMode;
                    const textColor = isDark ? '#d1d5db' : '#374151';

                    try {
                        this.languageChart = new Chart(ctx, {
                            type: 'doughnut',
                            data: chartData,
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        position: 'bottom',
                                        labels: {
                                            color: textColor,
                                            padding: 20,
                                            usePointStyle: true
                                        }
                                    }
                                }
                            }
                        });

                        console.log('Language chart created successfully');
                    } catch (error) {
                        console.error('Error creating language chart:', error);
                    }
                },

                // 默认数据方法
                getDefaultTrendData() {
                    return {
                        labels: ['01/01', '01/02', '01/03', '01/04', '01/05', '01/06', '01/07'],
                        datasets: [{
                            label: '翻译次数',
                            data: [12, 19, 3, 5, 2, 3, 10],
                            borderColor: '#3b82f6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    };
                },

                getDefaultLanguageData() {
                    return {
                        labels: ['中文→英文', '英文→中文', '中文→日文', '其他'],
                        datasets: [{
                            data: [35, 30, 15, 20],
                            backgroundColor: ['#3b82f6', '#ef4444', '#10b981', '#f59e0b'],
                            borderWidth: 2,
                            borderColor: '#ffffff'
                        }]
                    };
                },

                // Authentication - 增强安全检查
                async checkAuth() {
                    try {
                        // 多重认证检查
                        const response = await fetch('/api/auth/check', {
                            credentials: 'include',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}`);
                        }

                        const data = await response.json();

                        if (!data.logged_in) {
                            this.redirectToLogin('未登录');
                            return;
                        }

                        this.userInfo = data.user_info;

                        // 严格检查管理员权限
                        if (!this.userInfo || !['admin', 'super_admin'].includes(this.userInfo.role)) {
                            this.redirectToLogin('权限不足');
                            return;
                        }

                        // 验证用户信息完整性
                        if (!this.userInfo.user_number || !this.userInfo.user_name) {
                            this.redirectToLogin('用户信息不完整');
                            return;
                        }

                        // 设置安全标记
                        this.isAuthenticated = true;
                        this.startSecurityMonitoring();

                    } catch (error) {
                        console.error('认证检查失败:', error);
                        this.redirectToLogin('认证失败');
                    }
                },

                // 安全重定向
                redirectToLogin(reason) {
                    console.warn(`管理面板访问被拒绝: ${reason}`);
                    // 清除可能的敏感数据
                    this.userInfo = null;
                    this.isAuthenticated = false;
                    // 延迟重定向，防止快速重试
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 1000);
                },

                // 启动安全监控
                startSecurityMonitoring() {
                    // 定期检查认证状态
                    setInterval(async () => {
                        try {
                            const response = await fetch('/api/auth/check', {
                                credentials: 'include'
                            });
                            const data = await response.json();

                            if (!data.logged_in || !['admin', 'super_admin'].includes(data.user_info?.role)) {
                                this.redirectToLogin('会话过期');
                            }
                        } catch (error) {
                            console.warn('安全检查失败:', error);
                        }
                    }, 300000); // 每5分钟检查一次

                    // 监听页面可见性变化
                    document.addEventListener('visibilitychange', () => {
                        if (!document.hidden) {
                            // 页面重新可见时检查认证状态
                            this.checkAuth();
                        }
                    });
                },

                // Dashboard
                async loadDashboard() {
                    try {
                        this.loading = true;
                        const response = await fetch('/admin/dashboard', {
                            credentials: 'include',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });
                        const data = await response.json();

                        if (data.status === 'success') {
                            this.dashboardData = data.data;
                        }
                    } catch (error) {
                        console.error('加载仪表板数据失败:', error);
                        this.showNotification('加载仪表板数据失败', 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                // Users Management
                async addUser() {
                    try {
                        this.loading = true;
                        const response = await fetch('/admin/users', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(this.newUser)
                        });

                        const data = await response.json();
                        if (data.status === 'success') {
                            this.showNotification('用户添加成功', 'success');
                            this.showAddUserModal = false;
                            this.resetNewUser();
                            this.loadUsers(); // 重新加载用户列表
                        } else {
                            this.showNotification(data.message || '添加用户失败', 'error');
                        }
                    } catch (error) {
                        console.error('添加用户失败:', error);
                        this.showNotification('添加用户失败', 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                resetNewUser() {
                    this.newUser = {
                        user_number: '',
                        username: '',
                        role: 'user'
                    };
                },

                async loadUsers(page = 1) {
                    try {
                        this.loading = true;
                        const params = new URLSearchParams({
                            page: page,
                            per_page: this.userFilters.per_page,
                            search: this.userFilters.search,
                            role: this.userFilters.role
                        });

                        const response = await fetch(`/admin/users?${params}`, {
                            credentials: 'include',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });
                        const data = await response.json();

                        if (data.status === 'success') {
                            this.users = data.data.users;
                            this.userPagination = data.data.pagination;
                        }
                    } catch (error) {
                        console.error('加载用户列表失败:', error);
                        this.showNotification('加载用户列表失败', 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                // User Actions
                banUser(user) {
                    this.selectedUser = user;
                    this.banReason = '';
                    this.showBanModal = true;
                },

                async confirmBan() {
                    try {
                        const response = await fetch(`/admin/users/${this.selectedUser.user_number}/ban`, {
                            method: 'POST',
                            credentials: 'include',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            body: JSON.stringify({
                                reason: this.banReason || '违反平台规定'
                            })
                        });

                        const data = await response.json();

                        if (data.status === 'success') {
                            this.showNotification('用户已封禁', 'success');
                            this.showBanModal = false;
                            await this.loadUsers();
                        } else {
                            this.showNotification(data.message || '封禁失败', 'error');
                        }
                    } catch (error) {
                        console.error('封禁用户失败:', error);
                        this.showNotification('封禁用户失败', 'error');
                    }
                },

                async unbanUser(user) {
                    if (!confirm(`确定要解封用户 ${user.username} 吗？`)) {
                        return;
                    }

                    try {
                        const response = await fetch(`/admin/users/${user.user_number}/unban`, {
                            method: 'POST',
                            credentials: 'include',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });

                        const data = await response.json();

                        if (data.status === 'success') {
                            this.showNotification('用户已解封', 'success');
                            await this.loadUsers();
                        } else {
                            this.showNotification(data.message || '解封失败', 'error');
                        }
                    } catch (error) {
                        console.error('解封用户失败:', error);
                        this.showNotification('解封用户失败', 'error');
                    }
                },

                async grantAdmin(user) {
                    if (!confirm(`确定要授予 ${user.username} 管理员权限吗？`)) {
                        return;
                    }

                    try {
                        const response = await fetch(`/admin/users/${user.user_number}/grant`, {
                            method: 'POST',
                            credentials: 'include',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });

                        const data = await response.json();

                        if (data.status === 'success') {
                            this.showNotification('已授予管理员权限', 'success');
                            await this.loadUsers();
                        } else {
                            this.showNotification(data.message || '授权失败', 'error');
                        }
                    } catch (error) {
                        console.error('授权失败:', error);
                        this.showNotification('授权失败', 'error');
                    }
                },

                async revokeAdmin(user) {
                    if (!confirm(`确定要撤销 ${user.username} 的管理员权限吗？`)) {
                        return;
                    }

                    try {
                        const response = await fetch(`/admin/users/${user.user_number}/revoke`, {
                            method: 'POST',
                            credentials: 'include',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });

                        const data = await response.json();

                        if (data.status === 'success') {
                            this.showNotification('已撤销管理员权限', 'success');
                            await this.loadUsers();
                        } else {
                            this.showNotification(data.message || '撤权失败', 'error');
                        }
                    } catch (error) {
                        console.error('撤权失败:', error);
                        this.showNotification('撤权失败', 'error');
                    }
                },

                // Utility Methods
                getRoleText(role) {
                    const roleMap = {
                        'user': '普通用户',
                        'admin': '管理员',
                        'super_admin': '超级管理员'
                    };
                    return roleMap[role] || role;
                },

                getRoleBadgeClass(role) {
                    const classMap = {
                        'user': 'badge-info',
                        'admin': 'badge-warning',
                        'super_admin': 'badge-danger'
                    };
                    return classMap[role] || 'badge-info';
                },

                // Blacklist Management
                async loadBlacklist(page = 1) {
                    try {
                        this.loading = true;
                        const params = new URLSearchParams({
                            page: page,
                            per_page: 20
                        });

                        const response = await fetch(`/admin/blacklist?${params}`, {
                            credentials: 'include',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });
                        const data = await response.json();

                        if (data.status === 'success') {
                            this.blacklist = data.data.blacklist;
                            this.blacklistPagination = data.data.pagination;
                        }
                    } catch (error) {
                        console.error('加载黑名单失败:', error);
                        this.showNotification('加载黑名单失败', 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                async unbanUserFromBlacklist(entry) {
                    if (!confirm(`确定要解封用户 ${entry.username} 吗？`)) {
                        return;
                    }

                    try {
                        const response = await fetch(`/admin/users/${entry.user_number}/unban`, {
                            method: 'POST',
                            credentials: 'include',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });

                        const data = await response.json();

                        if (data.status === 'success') {
                            this.showNotification('用户已解封', 'success');
                            await this.loadBlacklist();
                        } else {
                            this.showNotification(data.message || '解封失败', 'error');
                        }
                    } catch (error) {
                        console.error('解封用户失败:', error);
                        this.showNotification('解封用户失败', 'error');
                    }
                },

                // Translations Management
                async loadTranslations(page = 1) {
                    try {
                        this.loading = true;
                        const params = new URLSearchParams({
                            page: page,
                            per_page: this.translationFilters.per_page,
                            user_number: this.translationFilters.user_number
                        });

                        const response = await fetch(`/admin/translations?${params}`, {
                            credentials: 'include',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });
                        const data = await response.json();

                        if (data.status === 'success') {
                            this.translations = data.data.translations;
                            this.translationPagination = data.data.pagination;
                        }
                    } catch (error) {
                        console.error('加载翻译记录失败:', error);
                        this.showNotification('加载翻译记录失败', 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                viewTranslationDetail(translation) {
                    // 简单的详情显示
                    const detail = `翻译详情：

用户：${translation.user_name || translation.username || 'N/A'} (${translation.user_number || 'N/A'})
语言对：${translation.source_language || 'N/A'} → ${translation.target_language || 'N/A'}
模型：${translation.model_used || 'N/A'}
时间：${this.formatDate(translation.created_at)}

原文：
${translation.source_text || 'N/A'}

译文：
${translation.translated_text || 'N/A'}`;

                    alert(detail);
                },

                // 文本单元格拖拽调整
                initResize(event, cellId) {
                    event.preventDefault();
                    event.stopPropagation();

                    const textCell = event.currentTarget.closest('.text-cell');
                    const startX = event.clientX;
                    const startWidth = textCell.offsetWidth;
                    const minWidth = 150;
                    const maxWidth = 600;

                    const doDrag = (e) => {
                        const currentX = e.clientX;
                        const diff = currentX - startX;
                        let newWidth = startWidth + diff;

                        // 限制宽度范围
                        newWidth = Math.max(minWidth, Math.min(maxWidth, newWidth));

                        // 应用新宽度到文本单元格和其父容器
                        textCell.style.width = newWidth + 'px';
                        textCell.parentElement.style.width = newWidth + 'px';
                        textCell.parentElement.style.minWidth = newWidth + 'px';
                        textCell.parentElement.style.maxWidth = newWidth + 'px';
                    };

                    const stopDrag = () => {
                        document.removeEventListener('mousemove', doDrag);
                        document.removeEventListener('mouseup', stopDrag);
                        document.body.style.cursor = '';
                        document.body.style.userSelect = '';
                    };

                    document.addEventListener('mousemove', doDrag);
                    document.addEventListener('mouseup', stopDrag);
                    document.body.style.cursor = 'col-resize';
                    document.body.style.userSelect = 'none';
                },



                // 导出功能
                async exportUsers() {
                    try {
                        this.loading = true;
                        // 获取所有用户数据
                        const response = await fetch('/admin/users?per_page=10000');

                        if (!response.ok) {
                            throw new Error('获取用户数据失败');
                        }

                        const data = await response.json();
                        if (data.status !== 'success' || !data.data || !data.data.users || data.data.users.length === 0) {
                            this.showNotification('没有可导出的用户数据', 'warning');
                            return;
                        }

                        const csvContent = this.generateUserCSVFromData(data.data.users);
                        this.downloadCSV(csvContent, 'users.csv');
                        this.showNotification('用户数据导出成功', 'success');
                    } catch (error) {
                        console.error('导出用户数据失败:', error);
                        this.showNotification('导出用户数据失败: ' + error.message, 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                async exportTranslations() {
                    try {
                        this.loading = true;
                        // 获取所有翻译记录
                        const response = await fetch('/admin/translations?per_page=10000');

                        if (!response.ok) {
                            throw new Error('获取翻译记录失败');
                        }

                        const data = await response.json();
                        if (data.status !== 'success' || !data.data || !data.data.translations || data.data.translations.length === 0) {
                            this.showNotification('没有可导出的翻译记录', 'warning');
                            return;
                        }

                        const csvContent = this.generateTranslationCSVFromData(data.data.translations);
                        this.downloadCSV(csvContent, 'translations.csv');
                        this.showNotification('翻译记录导出成功', 'success');
                    } catch (error) {
                        console.error('导出翻译记录失败:', error);
                        this.showNotification('导出翻译记录失败: ' + error.message, 'error');
                    } finally {
                        this.loading = false;
                    }
                },



                generateUserCSV() {
                    const headers = ['学号', '姓名', '角色', '注册时间', '最后登录'];
                    const rows = this.users.map(user => [
                        user.user_number,
                        user.username,
                        user.role,
                        this.formatDateOnly(user.created_at),
                        user.last_login ? this.formatDateOnly(user.last_login) : '从未登录'
                    ]);

                    return this.arrayToCSV([headers, ...rows]);
                },

                generateUserCSVFromData(users) {
                    const headers = ['学号', '姓名', '角色', '注册时间', '最后登录'];
                    const rows = users.map(user => [
                        user.user_number,
                        user.username,
                        user.role,
                        this.formatDateOnly(user.created_at),
                        user.last_login ? this.formatDateOnly(user.last_login) : '从未登录'
                    ]);

                    return this.arrayToCSV([headers, ...rows]);
                },

                generateTranslationCSV() {
                    const headers = ['用户学号', '用户姓名', '原文', '译文', '源语言', '目标语言', '模型', '翻译时间'];
                    const rows = this.translations.map(trans => [
                        trans.user_number || 'N/A',
                        trans.user_name || trans.username || 'N/A',
                        trans.source_text || 'N/A',
                        trans.translated_text || 'N/A',
                        trans.source_language || 'N/A',
                        trans.target_language || 'N/A',
                        trans.model_used || 'N/A',
                        this.formatDateOnly(trans.created_at)
                    ]);

                    return this.arrayToCSV([headers, ...rows]);
                },

                generateTranslationCSVFromData(translations) {
                    const headers = ['用户学号', '用户姓名', '原文', '译文', '源语言', '目标语言', '模型', '翻译时间'];
                    const rows = translations.map(trans => [
                        trans.user_number || 'N/A',
                        trans.user_name || trans.username || 'N/A',
                        trans.source_text || 'N/A',
                        trans.translated_text || 'N/A',
                        trans.source_language || 'N/A',
                        trans.target_language || 'N/A',
                        trans.model_used || 'N/A',
                        this.formatDateOnly(trans.created_at)
                    ]);

                    return this.arrayToCSV([headers, ...rows]);
                },

                arrayToCSV(data) {
                    return data.map(row =>
                        row.map(field => `"${String(field).replace(/"/g, '""')}"`)
                           .join(',')
                    ).join('\n');
                },

                async exportLogs() {
                    try {
                        this.loading = true;
                        // 获取所有操作日志
                        const response = await fetch('/admin/logs?per_page=10000', {
                            credentials: 'include',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });

                        if (!response.ok) {
                            throw new Error('获取操作日志失败');
                        }

                        const data = await response.json();
                        if (!data.data || !data.data.logs || data.data.logs.length === 0) {
                            this.showNotification('没有可导出的日志数据', 'warning');
                            return;
                        }

                        const csvContent = this.generateLogsCSVFromData(data.data.logs);
                        this.downloadCSV(csvContent, 'admin_logs.csv');
                        this.showNotification('操作日志导出成功', 'success');
                    } catch (error) {
                        console.error('导出操作日志失败:', error);
                        this.showNotification('导出操作日志失败: ' + error.message, 'error');
                    } finally {
                        this.loading = false;
                    }
                },



                generateLogsCSV() {
                    const headers = ['时间', '操作者学号', '操作者姓名', '操作', '描述', '状态'];
                    const rows = this.logs.map(log => [
                        this.formatDateOnly(log.created_at),
                        log.operator_user_number || '-',
                        log.operator_username || '-',
                        this.getActionText(log.action),
                        log.description || '-',
                        log.status === 'success' ? '成功' : '失败'
                    ]);

                    return this.arrayToCSV([headers, ...rows]);
                },

                generateLogsCSVFromData(logs) {
                    const headers = ['时间', '操作者学号', '操作者姓名', '操作', '描述', '状态'];
                    const rows = logs.map(log => [
                        this.formatDateOnly(log.created_at),
                        log.operator_user_number || '-',
                        log.operator_username || '-',
                        this.getActionText(log.action),
                        log.description || '-',
                        log.status === 'success' ? '成功' : '失败'
                    ]);

                    return this.arrayToCSV([headers, ...rows]);
                },

                downloadCSV(csvContent, filename) {
                    const BOM = '\uFEFF'; // UTF-8 BOM for Excel compatibility
                    const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
                    const link = document.createElement('a');
                    const url = URL.createObjectURL(blob);
                    link.setAttribute('href', url);
                    link.setAttribute('download', filename);
                    link.style.visibility = 'hidden';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                },

                // Analytics
                async loadUserAnalytics() {
                    try {
                        const response = await fetch('/admin/analytics/users', {
                            credentials: 'include',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });
                        const data = await response.json();

                        if (data.status === 'success') {
                            this.userAnalytics = data.data;
                        }
                    } catch (error) {
                        console.error('加载用户分析数据失败:', error);
                    }
                },

                async loadTranslationAnalytics() {
                    try {
                        const response = await fetch('/admin/analytics/translations', {
                            credentials: 'include',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });
                        const data = await response.json();

                        if (data.status === 'success') {
                            this.translationAnalytics = data.data;
                        }
                    } catch (error) {
                        console.error('加载翻译分析数据失败:', error);
                    }
                },



                // Logs Management
                async loadLogs(page = 1) {
                    try {
                        this.loading = true;
                        const params = new URLSearchParams({
                            page: page,
                            per_page: this.logFilters.per_page,
                            action: this.logFilters.action,
                            operator: this.logFilters.operator
                        });

                        const response = await fetch(`/admin/logs?${params}`, {
                            credentials: 'include',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            }
                        });
                        const data = await response.json();

                        if (data.status === 'success') {
                            this.logs = data.data.logs;
                            this.logsPagination = data.data.pagination;
                            this.availableActions = data.data.available_actions;
                        }
                    } catch (error) {
                        console.error('加载操作日志失败:', error);
                        this.showNotification('加载操作日志失败', 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                // Utility Methods
                formatDate(dateString) {
                    if (!dateString) return '-';
                    const date = new Date(dateString);
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                },

                formatDateOnly(dateString) {
                    if (!dateString) return '-';
                    const date = new Date(dateString);
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}/${month}/${day}`;
                },

                getActionText(action) {
                    const actionMap = {
                        'view_dashboard': '查看仪表板',
                        'view_users': '查看用户列表',
                        'ban_user': '封禁用户',
                        'unban_user': '解封用户',
                        'grant_admin': '授予管理员权限',
                        'revoke_admin': '撤销管理员权限',
                        'view_translations': '查看翻译记录',
                        'view_translation_detail': '查看翻译详情',
                        'view_user_analytics': '查看用户分析',
                        'view_translation_analytics': '查看翻译分析',
                        'view_blacklist': '查看黑名单',
                        'view_admin_logs': '查看操作日志',
                        'init_super_admin': '初始化超级管理员'
                    };
                    return actionMap[action] || action;
                },

                showNotification(message, type = 'info') {
                    // Create notification element
                    const notification = document.createElement('div');
                    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 text-white transition-all duration-300 transform translate-x-full`;

                    switch (type) {
                        case 'success':
                            notification.classList.add('bg-green-500');
                            break;
                        case 'error':
                            notification.classList.add('bg-red-500');
                            break;
                        case 'warning':
                            notification.classList.add('bg-yellow-500');
                            break;
                        default:
                            notification.classList.add('bg-blue-500');
                    }

                    notification.textContent = message;
                    document.body.appendChild(notification);

                    // Animate in
                    setTimeout(() => {
                        notification.classList.remove('translate-x-full');
                    }, 100);

                    // Remove after 3 seconds
                    setTimeout(() => {
                        notification.classList.add('translate-x-full');
                        setTimeout(() => {
                            document.body.removeChild(notification);
                        }, 300);
                    }, 3000);
                },

                // 列宽调整功能
                startResize(event, columnIndex) {
                    event.preventDefault();
                    const table = event.target.closest('table');
                    const th = event.target.closest('th');
                    const resizer = event.target;

                    // 添加调整中的样式
                    resizer.classList.add('resizing');

                    const startX = event.clientX;
                    const startWidth = th.offsetWidth;

                    const onMouseMove = (e) => {
                        const diff = e.clientX - startX;
                        const newWidth = Math.max(100, startWidth + diff); // 最小宽度100px
                        th.style.width = newWidth + 'px';
                        th.style.minWidth = newWidth + 'px';
                    };

                    const onMouseUp = () => {
                        resizer.classList.remove('resizing');
                        document.removeEventListener('mousemove', onMouseMove);
                        document.removeEventListener('mouseup', onMouseUp);
                        document.body.style.cursor = '';
                        document.body.style.userSelect = '';
                    };

                    document.addEventListener('mousemove', onMouseMove);
                    document.addEventListener('mouseup', onMouseUp);
                    document.body.style.cursor = 'col-resize';
                    document.body.style.userSelect = 'none';
                }
            },

            watch: {
                currentView(newView) {
                    switch (newView) {
                        case 'users':
                            this.loadUsers();
                            break;
                        case 'blacklist':
                            this.loadBlacklist();
                            break;
                        case 'translations':
                            this.loadTranslations();
                            break;
                        case 'analytics':
                            this.loadUserAnalytics();
                            this.loadTranslationAnalytics();
                            break;
                        case 'logs':
                            this.loadLogs();
                            break;
                    }
                }
            }
        }).mount('#app');
    </script>
</body>

</html>

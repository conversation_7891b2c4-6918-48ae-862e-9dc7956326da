import os
import requests
import json
from flask import Blueprint, jsonify, request, session, redirect, make_response
from urllib.parse import urlencode
import time
from src.models.user import User, db
from src.models.login_history import LoginHistory
from src.utils.token_auth import TokenAuth, require_auth

auth_bp = Blueprint('auth', __name__)

# 从环境变量获取配置
HENAU_APPID = os.getenv('HENAU_APPID', 'your_app_id')
HENAU_SECRET = os.getenv('HENAU_SECRET', 'your_app_secret')
REDIRECT_URI = os.getenv('REDIRECT_URI', 'http://localhost:5000/api/auth/callback')

@auth_bp.route('/login', methods=['GET'])
def login():
    """生成登录二维码URL"""
    state = 'random_state_' + str(hash(request.remote_addr))

    auth_url = 'https://oauth.henau.edu.cn/oauth2_qr_connect/login'
    params = {
        'appid': HENAU_APPID,
        'redirect_uri': REDIRECT_URI,
        'response_type': 'code',
        'scope': 'henauapi_login',
        'state': state
    }

    qr_url = f"{auth_url}?{urlencode(params)}"
    session['oauth_state'] = state

    return jsonify({'status': 'success', 'qr_url': qr_url, 'state': state})

@auth_bp.route('/callback', methods=['GET'])
def callback():
    code = request.args.get('code')
    state = request.args.get('state')

    if not code:
        return jsonify({'status': 'error', 'message': '授权失败，未获取到code'}), 400
    # if state != session.get('oauth_state'):
    #     return jsonify({'status': 'error', 'message': 'state验证失败'}), 400
    try:
        token_url = 'https://oauth.henau.edu.cn/oauth2_server/access_token'
        token_params = {
            'appid': HENAU_APPID,
            'secret': HENAU_SECRET,
            'code': code,
            'grant_type': 'authorization_code'
        }
        token_data = requests.get(token_url, params=token_params).json()

        if token_data.get('status') != 'success':
            return jsonify({'status': 'error', 'message': '获取access_token失败', 'data': token_data}), 400
        access_token = token_data['data']['access_token']
        henau_openid = token_data['data']['henau_openid']

        userinfo_url = 'https://oauth.henau.edu.cn/oauth2_server/userinfo'
        userinfo_params = {'access_token': access_token, 'henau_openid': henau_openid}
        userinfo_data = requests.get(userinfo_url, params=userinfo_params).json()
        if userinfo_data.get('status') != 'success':
            return jsonify({'status': 'error', 'message': '获取用户信息失败', 'data': userinfo_data}), 400

        # ----- 黑名单检查 -----
        from src.models.blacklist import Blacklist  # 本地导入避免循环
        if Blacklist.query.filter_by(user_number=userinfo_data['data'].get('user_number'), is_active=True).first():
            return jsonify({'status': 'error', 'message': '账号已被封禁，无法登录'}), 403

        # 将用户信息写入数据库
        user_number = userinfo_data['data'].get('user_number') or userinfo_data['data'].get('student_number')
        user_name = userinfo_data['data'].get('user_name') or userinfo_data['data'].get('name')
        user_section = userinfo_data['data'].get('user_section')  # 学院信息

        if user_number:
            user = User.query.filter_by(user_number=user_number).first()
            if not user:
                user = User(user_number=user_number, username=user_name or user_number, user_section=user_section)
                db.session.add(user)
            else:
                # 更新关键信息
                user.username = user_name or user.username

                if user_section:
                    user.user_section = user_section

            # 提交数据库更改以获取最新的用户信息（包括role）
            db.session.commit()

            # 重新查询用户以获取完整信息（包括role）
            user = User.query.filter_by(user_number=user_number).first()

            # 构建完整的用户信息（包含role）
            complete_user_info = userinfo_data['data'].copy()
            complete_user_info['role'] = user.role if user else 'user'
            complete_user_info['user_number'] = user_number
            complete_user_info['user_name'] = user_name
            if user_section:
                complete_user_info['user_section'] = user_section

        # 保存完整用户信息到session
        session['user_info'] = complete_user_info
        session['access_token'] = access_token
        session['henau_openid'] = henau_openid
        session.permanent = True  # 设置为永久session

        # 生成JWT token
        token = TokenAuth.generate_token(complete_user_info)

        # 添加登录历史
        if user_number:
            login_record = LoginHistory(
                user_number=user_number,
                user_name=user_name,
                user_section=user_section,
                login_type="oauth",
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent'),
                is_active=True
            )
            db.session.add(login_record)
            db.session.commit()

            # 标记管理员权限到session
            session['is_admin'] = user.role in ['admin', 'super_admin']

        # 返回成功页面，设置token到cookie并重定向
        redirect_url = '/?login=success&t=' + str(int(__import__('time').time()))
        response = make_response(redirect(redirect_url))
        if token:
            # 根据环境自动判断是否使用secure
            is_production = os.getenv('FLASK_ENV') == 'production' or not os.getenv('FLASK_DEBUG', '').lower() in ['true', '1']
            
            # 设置token到cookie（HttpOnly, Secure）
            response.set_cookie(
                'auth_token', 
                token, 
                max_age=24*60*60,  # 24小时
                httponly=True,
                secure=is_production,  # 生产环境自动启用secure
                samesite='Lax'
            )
        return response
    except Exception as e:
        return jsonify({'status': 'error', 'message': f'登录过程中发生错误: {str(e)}'}), 500

@auth_bp.route('/userinfo', methods=['GET'])
@require_auth
def get_userinfo():
    user_info = session.get('user_info')
    if not user_info and hasattr(request, 'user_info'):
        user_info = request.user_info
    if not user_info:
        return jsonify({'status': 'error', 'message': '用户未登录'}), 401
    return jsonify({'status': 'success', 'data': user_info})

@auth_bp.route('/logout', methods=['POST'])
def logout():
    session.clear()
    response = make_response(jsonify({'status': 'success', 'message': '登出成功'}))
    # 清除token cookie
    response.set_cookie('auth_token', '', expires=0)
    return response

@auth_bp.route('/check', methods=['GET'])
def check_auth():
    # 首先检查session
    access_token = session.get('access_token')
    user_info = session.get('user_info', {})
    
    if access_token and user_info:
        return jsonify({
            'status': 'success',
            'message': '用户已登录',
            'logged_in': True,
            'user_info': user_info,
            'is_admin': session.get('is_admin', False)
        })
    
    # 然后检查token
    token = TokenAuth.get_token_from_request()
    if token:
        payload = TokenAuth.verify_token(token)
        if payload:
            return jsonify({
                'status': 'success',
                'message': '用户已登录',
                'logged_in': True,
                'user_info': payload,
            'is_admin': payload.get('role') in ['admin', 'super_admin'] if isinstance(payload, dict) else False
            })
    
    return jsonify({'status': 'error', 'message': '用户未登录', 'logged_in': False})
    # 无需远程验证，直接基于session判断
    # 如果需要远程验证，可以恢复以下代码：
    # try:
    #     auth_url = 'https://oauth.henau.edu.cn/oauth2_server/auth'
    #     auth_params = {'access_token': access_token, 'henau_openid': henau_openid}
    #     auth_data = requests.get(auth_url, params=auth_params).json()
    #     if auth_data.get('errcode') == 0:
    #         return jsonify({'status': 'success', 'message': '用户已登录', 'logged_in': True, 'user_info': session.get('user_info')})
    #     session.clear()
    #     return jsonify({'status': 'error', 'message': 'token已失效', 'logged_in': False}), 401
    # except Exception as e:
    #     return jsonify({'status': 'error', 'message': f'验证登录状态时发生错误: {str(e)}', 'logged_in': False}), 500

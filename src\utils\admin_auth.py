from functools import wraps
from flask import session, jsonify, request, g
from ..models.user import User
from ..models.blacklist import Blacklist
from ..models.admin_log import AdminLog
import hashlib
from ..models.db import db

def generate_session_fingerprint(request):
    """生成session指纹用于防止会话劫持"""
    user_agent = request.headers.get('User-Agent', '')
    accept_language = request.headers.get('Accept-Language', '')
    accept_encoding = request.headers.get('Accept-Encoding', '')

    # 组合多个请求头信息生成指纹
    fingerprint_data = f"{user_agent}|{accept_language}|{accept_encoding}"
    return hashlib.sha256(fingerprint_data.encode()).hexdigest()[:16]

def require_admin(f):
    """管理员权限装饰器 - 增强安全检查"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 多层安全验证
        user_info = session.get('user_info')
        if not user_info:
            # 记录未授权访问尝试
            print(f"Unauthorized admin API access attempt from {request.remote_addr}")
            return jsonify({'status': 'error', 'message': '未登录'}), 401

        user_number = user_info.get('user_number') or user_info.get('student_number')
        if not user_number:
            return jsonify({'status': 'error', 'message': '用户信息不完整'}), 401

        # 数据库验证用户权限
        user = User.query.filter_by(user_number=user_number).first()
        if not user or user.role not in ['admin', 'super_admin']:
            print(f"Insufficient privileges for user {user_number} from {request.remote_addr}")
            return jsonify({'status': 'error', 'message': '权限不足'}), 403

        # 检查黑名单状态
        if is_user_blacklisted(user_number):
            print(f"Blacklisted user {user_number} attempted admin access from {request.remote_addr}")
            return jsonify({'status': 'error', 'message': '账号已被封禁'}), 403

        # 验证session完整性（防止session劫持） - 放宽检查
        expected_session_id = generate_session_fingerprint(request)
        stored_session_id = session.get('session_fingerprint')
        if not stored_session_id:
            # 首次访问，生成指纹
            session['session_fingerprint'] = expected_session_id
        elif stored_session_id != expected_session_id:
            # Session可能被劫持，但不立即清除session，只记录警告
            print(f"Session fingerprint mismatch for user {user_number} from {request.remote_addr} - updating fingerprint")
            # 更新指纹而不是清除session（可能是浏览器更新或网络变化）
            session['session_fingerprint'] = expected_session_id

        # 将用户信息传递给路由函数（使用g对象而不是request）
        from flask import g
        g.current_admin = user
        return f(*args, **kwargs)
    return decorated_function

def require_super_admin(f):
    """超级管理员权限装饰器 - 增强安全检查"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user_info = session.get('user_info')
        if not user_info:
            print(f"Unauthorized super admin API access attempt from {request.remote_addr}")
            return jsonify({'status': 'error', 'message': '未登录'}), 401

        user_number = user_info.get('user_number') or user_info.get('student_number')
        if not user_number:
            return jsonify({'status': 'error', 'message': '用户信息不完整'}), 401

        user = User.query.filter_by(user_number=user_number).first()
        if not user or user.role != 'super_admin':
            print(f"Insufficient super admin privileges for user {user_number} from {request.remote_addr}")
            return jsonify({'status': 'error', 'message': '需要超级管理员权限'}), 403

        # 检查黑名单状态
        if is_user_blacklisted(user_number):
            print(f"Blacklisted super admin {user_number} attempted access from {request.remote_addr}")
            return jsonify({'status': 'error', 'message': '账号已被封禁'}), 403

        # 验证session完整性 - 放宽检查
        expected_session_id = generate_session_fingerprint(request)
        stored_session_id = session.get('session_fingerprint')
        if not stored_session_id:
            session['session_fingerprint'] = expected_session_id
        elif stored_session_id != expected_session_id:
            print(f"Session fingerprint mismatch for super admin {user_number} from {request.remote_addr} - updating fingerprint")
            # 更新指纹而不是清除session
            session['session_fingerprint'] = expected_session_id

        # 将用户信息传递给路由函数
        g.current_admin = user
        return f(*args, **kwargs)
    return decorated_function

def is_user_blacklisted(user_number):
    """检查用户是否在黑名单中"""
    blacklist_entry = Blacklist.query.filter_by(
        user_number=user_number, 
        is_active=True
    ).first()
    return blacklist_entry is not None

def clear_user_sessions(user_number):
    """清除指定用户的所有session（暂未实现）"""
    # TODO: 实现session清除功能
    # 建议使用Redis存储session或在token验证时检查黑名单状态
    pass

def log_admin_action(action, description, target_user_number=None, target_username=None, 
                    details=None, status='success', error_message=None):
    """记录管理员操作日志的便捷函数"""
    if not hasattr(g, 'current_admin'):
        return

    admin = g.current_admin
    AdminLog.log_action(
        operator_user_number=admin.user_number,
        operator_username=admin.username,
        operator_role=admin.role,
        action=action,
        description=description,
        target_user_number=target_user_number,
        target_username=target_username,
        details=details,
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent'),
        status=status,
        error_message=error_message
    )
    db.session.commit()

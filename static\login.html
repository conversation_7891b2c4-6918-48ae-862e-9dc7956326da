<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- 保留 v-cloak 隐藏并添加渐显效果 -->
    <style>
        [v-cloak] {
            display: none;
        }

        html:not(.loaded) body {
            opacity: 0;
        }

        html.loaded body {
            opacity: 1;
            transition: opacity 0.5s ease-in-out;
        }

        /* 防止动画元素在动画开始前闪现 */
        .animate-slide-in-left {
            opacity: 0;
            transform: translateX(-50px);
        }

        .animate-slide-in-right {
            opacity: 0;
            transform: translateX(50px);
        }

        /* 页面加载完成后启用动画 */
        html.loaded .animate-slide-in-left {
            animation: slideInLeft 0.8s ease-out forwards;
        }

        html.loaded .animate-slide-in-right {
            animation: slideInRight 0.8s ease-out forwards;
        }

        /* 支持内联样式的延迟动画 */
        html.loaded .animate-slide-in-left[style*="animation-delay"] {
            animation: slideInLeft 0.8s ease-out forwards;
        }

        html.loaded .animate-slide-in-right[style*="animation-delay"] {
            animation: slideInRight 0.8s ease-out forwards;
        }
    </style>
    <title>河南农业大学智能翻译平台 </title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="HenauLogo.png">
    <link rel="shortcut icon" type="image/png" href="HenauLogo.png">
    <link rel="apple-touch-icon" href="HenauLogo.png">
    <link href="fallback.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="tailwind.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="vue.js" onerror="this.onerror=null; this.src='https://unpkg.com/vue@3/dist/vue.global.js'"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a'
                        }
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'fade-in': 'fadeIn 1s ease-out',
                        'slide-up': 'slideUp 0.8s ease-out',
                        'slide-in-left': 'slideInLeft 0.8s ease-out',
                        'slide-in-right': 'slideInRight 0.8s ease-out',
                        'glow': 'glow 2s ease-in-out infinite alternate'
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' }
                        },
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(50px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        },
                        slideInLeft: {
                            '0%': { transform: 'translateX(-50px)', opacity: '0' },
                            '100%': { transform: 'translateX(0)', opacity: '1' }
                        },
                        slideInRight: {
                            '0%': { transform: 'translateX(50px)', opacity: '0' },
                            '100%': { transform: 'translateX(0)', opacity: '1' }
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 20px rgba(59, 130, 246, 0.5)' },
                            '100%': { boxShadow: '0 0 40px rgba(59, 130, 246, 0.8)' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        /* 鼠标跟随光晕元素样式 */
        .cursor-glow {
            position: fixed;
            left: 0;
            top: 0;
            width: 160px;
            height: 160px;
            border-radius: 50%;
            pointer-events: none;
            background: radial-gradient(circle, rgba(96, 165, 250, 0.45) 0%, rgba(96, 165, 250, 0) 70%);
            transform: translate(-50%, -50%);
            mix-blend-mode: screen;
            z-index: 50;
        }
    </style>
    <style>
        [v-cloak] {
            display: none;
        }

        /* 优化初始加载状态 */
        .loading-fade {
            opacity: 0;
            animation: fadeInSlow 0.8s ease-out forwards;
        }

        @keyframes fadeInSlow {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 预加载状态优化 */
        .preload {
            visibility: hidden;
        }

        .loaded .preload {
            visibility: visible;
        }

        .login-bg {
            background: linear-gradient(135deg,
                    #1e3a8a 0%,
                    #1d4ed8 25%,
                    #2563eb 50%,
                    #3b82f6 75%,
                    #60a5fa 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
        }

        @keyframes gradientShift {
            0% {
                background-position: 0% 50%;
            }

            50% {
                background-position: 100% 50%;
            }

            100% {
                background-position: 0% 50%;
            }
        }

        .glass-card {
            backdrop-filter: blur(20px) saturate(180%);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
        }

        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        .qr-container {
            transition: all 0.3s ease;
        }

        .qr-container:hover {
            transform: scale(1.02);
        }

        .loading-spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .pulse-ring {
            position: absolute;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: pulse-ring 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
        }

        @keyframes pulse-ring {
            0% {
                transform: scale(0.8);
                opacity: 1;
            }

            100% {
                transform: scale(2.5);
                opacity: 0;
            }
        }
    </style>
</head>

<body>
    <div id="app" v-cloak class="min-h-screen login-bg relative overflow-hidden">
        <!-- Floating Background Shapes -->
        <div id="particles-bg" class="absolute inset-0 z-0"></div>
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>

        <!-- Main Content -->
        <div class="relative z-10 min-h-screen flex items-center justify-center p-4">
            <div class="max-w-7xl w-full">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-16 items-center">
                    <!-- Desktop: Left Side - Platform Introduction -->
                    <div class="text-white animate-slide-in-left order-2 lg:order-1">
                        <div class="mb-8 hidden lg:block">
                            <div class="relative inline-block mb-6">
                                <div class="pulse-ring"></div>
                                <div
                                    class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center animate-glow">
                                    <i class="fas fa-language text-2xl text-white"></i>
                                </div>
                            </div>
                            <h1 class="text-4xl lg:text-5xl font-bold mb-4">
                                河南农业大学<br>
                                <span class="text-blue-300">智能翻译平台</span>
                            </h1>
                            <p class="text-white/80 text-lg mb-8">
                                基于先进的大语言模型技术，为师生提供精准高质量的多语言翻译服务
                            </p>
                        </div>

                        <!-- Desktop Features -->
                        <div class="space-y-6 hidden lg:block">
                            <div class="flex items-start space-x-4 animate-slide-in-left" style="animation-delay: 0.2s">
                                <div
                                    class="w-14 h-14 bg-blue-500 rounded-xl flex items-center justify-center flex-shrink-0 shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                                    <i class="fas fa-language text-white text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="text-xl font-semibold mb-2">智能语言识别</h3>
                                    <p class="text-white/70">支持12种语言，自动识别源语言，智能优化翻译</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-4 animate-slide-in-left" style="animation-delay: 0.4s">
                                <div
                                    class="w-14 h-14 bg-green-500 rounded-xl flex items-center justify-center flex-shrink-0 shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                                    <i class="fas fa-shield-alt text-white text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="text-xl font-semibold mb-2">安全可靠</h3>
                                    <p class="text-white/70">本地部署模型，确保数据安全，保护隐私信息</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-4 animate-slide-in-left" style="animation-delay: 0.6s">
                                <div
                                    class="w-14 h-14 bg-purple-500 rounded-xl flex items-center justify-center flex-shrink-0 shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                                    <i class="fas fa-user-check text-white text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="text-xl font-semibold mb-2">便捷登录</h3>
                                    <p class="text-white/70">使用河南农业大学统一身份认证，安全快捷</p>
                                </div>
                            </div>
                        </div>

                        <!-- Mobile: Features (below QR code) -->
                        <div class="lg:hidden mt-8">
                            <!-- Mobile Features -->
                            <div class="space-y-4">
                                <div class="flex items-start space-x-3 animate-slide-in-left" style="animation-delay: 0.2s">
                                    <div
                                        class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center flex-shrink-0 shadow-md">
                                        <i class="fas fa-language text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold mb-1">智能语言识别</h3>
                                        <p class="text-white/70 text-sm">支持12种语言，自动识别源语言，智能优化翻译</p>
                                    </div>
                                </div>

                                <div class="flex items-start space-x-3 animate-slide-in-left" style="animation-delay: 0.4s">
                                    <div
                                        class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center flex-shrink-0 shadow-md">
                                        <i class="fas fa-shield-alt text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold mb-1">安全可靠</h3>
                                        <p class="text-white/70 text-sm">本地部署模型，确保数据安全，保护隐私信息</p>
                                    </div>
                                </div>

                                <div class="flex items-start space-x-3 animate-slide-in-left" style="animation-delay: 0.6s">
                                    <div
                                        class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center flex-shrink-0 shadow-md">
                                        <i class="fas fa-user-check text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-lg font-semibold mb-1">便捷登录</h3>
                                        <p class="text-white/70 text-sm">使用河南农业大学统一身份认证，安全快捷</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- QR Code Login -->
                    <div class="flex justify-center lg:justify-end animate-slide-in-right order-1 lg:order-2"
                        style="animation-delay: 0.3s">
                        <!-- Mobile: Platform Introduction (above QR code) -->
                        <div class="w-full max-w-md">
                            <div class="lg:hidden mb-6 text-center">
                                <div class="relative inline-block mb-4">
                                    <div class="pulse-ring"></div>
                                    <div
                                        class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center animate-glow">
                                        <i class="fas fa-language text-xl text-white"></i>
                                    </div>
                                </div>
                                <h1 class="text-2xl font-bold mb-3 text-white">
                                    河南农业大学<br>
                                    <span class="text-blue-300">智能翻译平台</span>
                                </h1>
                                <p class="text-white/80 text-base mb-6">
                                    基于先进的大语言模型技术，为师生提供精准高质量的多语言翻译服务
                                </p>
                            </div>
                        <div class="glass-card rounded-3xl p-8 w-full max-w-md">
                            <div class="text-center mb-6">
                                <h2 class="text-2xl font-bold text-white mb-2">扫码登录</h2>
                                <p class="text-white/70">使用河南农业大学统一身份认证</p>
                            </div>

                            <div class="text-center">
                                <!-- 加载状态 -->
                                <div v-if="qrLoading" class="flex flex-col items-center justify-center py-12">
                                    <div class="loading-spinner mb-4"></div>
                                    <p class="text-white/80">正在生成登录二维码...</p>
                                </div>

                                <!-- 二维码显示 -->
                                <div v-else-if="qrUrl && qrUrl.length > 0" class="qr-container">
                                    <div
                                        class="bg-white rounded-2xl p-4 mb-6 animate-fade-in flex items-center justify-center">
                                        <iframe :src="qrUrl" width="280" height="380" frameborder="0"
                                            class="mx-auto block rounded-lg shadow-lg"></iframe>
                                    </div>
                                    <button @click="refreshQR"
                                        class="text-white/70 hover:text-white text-sm transition-colors duration-300">
                                        <i class="fas fa-refresh mr-1"></i>
                                        刷新二维码
                                    </button>
                                </div>

                                <!-- 错误状态 -->
                                <div v-else class="py-12">
                                    <div class="text-white/80 mb-6">
                                        <div
                                            class="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                            <i class="fas fa-wifi text-white/60 text-2xl"></i>
                                        </div>
                                        <h3 class="text-lg font-semibold mb-2">服务暂时不可用</h3>
                                        <p class="text-sm text-white/60">认证服务器连接失败，请稍后重试</p>
                                    </div>
                                    <button @click="loadQR"
                                        class="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-xl transition-all duration-300 backdrop-blur-sm">
                                        <i class="fas fa-redo mr-2"></i>
                                        重新连接
                                    </button>
                                </div>
                            </div>
                        </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success Animation Overlay -->
        <div v-if="loginSuccess" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div class="bg-white rounded-2xl p-8 text-center animate-slide-up">
                <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-check text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-2">登录成功！</h3>
                <p class="text-gray-600">正在跳转到翻译平台...</p>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    qrUrl: '',
                    qrLoading: true,
                    loginCheckInterval: null,
                    loginSuccess: false
                }
            },
            mounted() {
                this.loadQR();
                this.startLoginPolling();
                // 页面资源和 Vue 就绪后，显示主体并淡入
                document.documentElement.classList.add('loaded');
            },
            beforeUnmount() {
                this.stopLoginPolling();
            },
            methods: {
                async loadQR() {
                    this.qrLoading = true;
                    this.qrUrl = '';

                    try {
                        const response = await fetch('/api/auth/login');
                        const data = await response.json();

                        if (data.status === 'success' && data.qr_url) {
                            this.qrUrl = data.qr_url;
                            this.startLoginPolling();
                        } else {
                            console.error('获取登录二维码失败:', data.message || '未知错误');
                        }
                    } catch (error) {
                        console.error('网络错误:', error);
                    } finally {
                        this.qrLoading = false;
                    }
                },

                async refreshQR() {
                    await this.loadQR();
                },

                startLoginPolling() {
                    if (this.loginCheckInterval) {
                        clearInterval(this.loginCheckInterval);
                    }

                    this.loginCheckInterval = setInterval(async () => {
                        try {
                            const response = await fetch('/api/auth/check');
                            const data = await response.json();

                            if (data.logged_in) {
                                this.stopLoginPolling();
                                this.loginSuccess = true;

                                // 延迟跳转到对应页面
                                setTimeout(() => {
                                    // 检查是否有返回URL
                                    const returnUrl = sessionStorage.getItem('returnUrl');
                                    if (returnUrl) {
                                        sessionStorage.removeItem('returnUrl');
                                        window.location.href = returnUrl;
                                        return;
                                    }

                                    // 根据设备类型跳转
                                    function isMobile() {
                                        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                                               window.innerWidth <= 768;
                                    }

                                    if (isMobile()) {
                                        window.location.href = 'mobile.html';
                                    } else {
                                        window.location.href = '/';
                                    }
                                }, 2000);
                            }
                        } catch (error) {
                            console.error('检查登录状态失败:', error);
                        }
                    }, 2000);
                },

                stopLoginPolling() {
                    if (this.loginCheckInterval) {
                        clearInterval(this.loginCheckInterval);
                        this.loginCheckInterval = null;
                    }
                }
            }
        }).mount('#app');
    </script>
    <!-- 本地图粒子库，失败时使用 CDN -->
    <script src="particles.min.js"
        onerror="this.onerror=null; this.src='https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js'"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {

            /* 粒子背景初始化 */
            if (window.particlesJS) {
                particlesJS('particles-bg', {
                    particles: {
                        number: { value: 80 },
                        color: { value: '#ffffff' },
                        shape: { type: 'circle' },
                        opacity: { value: 0.4 },
                        size: { value: 3 },
                        line_linked: { enable: true, distance: 120, color: '#ffffff', opacity: 0.2, width: 1 },
                        move: { enable: true, speed: 1 }
                    },
                    interactivity: {
                        detect_on: 'canvas',
                        events: { onhover: { enable: true, mode: 'grab' } },
                        modes: { grab: { distance: 140, line_linked: { opacity: 0.3 } } }
                    },
                    retina_detect: true
                });
            }
        });
    </script>
    <!-- 光晕元素 -->
    <div id="cursorGlow" class="cursor-glow"></div>
</body>

</html>
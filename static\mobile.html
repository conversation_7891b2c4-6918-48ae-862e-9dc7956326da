<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>河南农业大学智能翻译平台 - 移动版</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="HenauLogo.png">
    <link rel="shortcut icon" type="image/png" href="HenauLogo.png">
    <link rel="apple-touch-icon" href="HenauLogo.png">

    <!-- 样式资源 -->
    <link href="fallback.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="tailwind.css" rel="stylesheet">
    <script src="vue.js" onerror="this.onerror=null; this.src='https://unpkg.com/vue@3/dist/vue.global.js'"></script>

    <script>
        // 动态加载Vue应用脚本
        document.addEventListener('DOMContentLoaded', function() {
            const script = document.createElement('script');
            script.src = 'mobile-app.js';
            document.head.appendChild(script);
        });
    </script>

    <script>
        // Tailwind配置
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>

    <style>
        [v-cloak] { display: none; }

        /* SVG图标样式 */
        .icon {
            width: 1em;
            height: 1em;
            display: inline-block;
            fill: currentColor;
            vertical-align: middle;
        }

        /* Apple风格的液体玻璃效果 - 增强版 */
        .glass-effect {
            backdrop-filter: blur(25px) saturate(200%);
            background: rgba(255, 255, 255, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.4);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 100;
        }

        .dark .glass-effect {
            background: rgba(31, 41, 55, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* 汉堡菜单动画 */
        .hamburger {
            width: 24px;
            height: 18px;
            position: relative;
            cursor: pointer;
            z-index: 1002;
            padding: 8px;
            margin: -8px;
        }

        .hamburger span {
            display: block;
            position: absolute;
            height: 2px;
            width: 100%;
            background: currentColor;
            border-radius: 1px;
            opacity: 1;
            left: 0;
            transform: rotate(0deg);
            transition: .25s ease-in-out;
        }

        .hamburger span:nth-child(1) { top: 0px; }
        .hamburger span:nth-child(2) { top: 8px; }
        .hamburger span:nth-child(3) { top: 16px; }

        .hamburger.active span:nth-child(1) {
            top: 8px;
            transform: rotate(135deg);
        }

        .hamburger.active span:nth-child(2) {
            opacity: 0;
            left: -60px;
        }

        .hamburger.active span:nth-child(3) {
            top: 8px;
            transform: rotate(-135deg);
        }

        /* 移动端侧边栏样式 */
        .mobile-sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 40;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .mobile-sidebar-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .mobile-sidebar {
            position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            width: 280px;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px) saturate(180%);
            z-index: 50;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            overflow-y: auto;
            border-right: 1px solid rgba(255, 255, 255, 0.3);
        }

        .dark .mobile-sidebar {
            background: rgba(15, 23, 42, 0.25);
            border-right: 1px solid rgba(255, 255, 255, 0.15);
        }

        .mobile-sidebar.active {
            transform: translateX(0);
        }

        /* 侧边栏菜单项 */
        .sidebar-menu-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            margin: 4px 12px;
            border-radius: 12px;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-weight: 500;
            color: #374151;
            backdrop-filter: blur(10px);
        }

        .dark .sidebar-menu-item {
            color: #e5e7eb;
            border: 1px solid rgba(255, 255, 255, 0.15);
        }

        .sidebar-menu-item:hover {
            background: rgba(59, 130, 246, 0.15);
            color: #3b82f6;
            transform: translateX(4px);
            backdrop-filter: blur(15px);
        }

        .dark .sidebar-menu-item:hover {
            background: rgba(59, 130, 246, 0.2);
            color: #60a5fa;
        }

        .sidebar-menu-item svg {
            width: 20px;
            height: 20px;
            margin-right: 12px;
        }

        /* 移动端优化的按钮样式 */
        .mobile-button {
            min-height: 44px;
            padding: 12px 20px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .mobile-button:active {
            transform: scale(0.98);
        }

        .mobile-button-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }

        .mobile-button-secondary {
            background: rgba(255, 255, 255, 0.9);
            color: #374151;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .dark .mobile-button-secondary {
            background: rgba(55, 65, 81, 0.9);
            color: #f3f4f6;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* 移动端文本区域样式 - 增强毛玻璃效果 */
        .mobile-textarea {
            width: 100%;
            min-height: 120px;
            padding: 16px;
            border: 1px solid rgba(255, 255, 255, 0.4);
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(25px) saturate(200%);
            font-size: 16px;
            line-height: 1.5;
            color: #374151;
            resize: none;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .mobile-textarea::placeholder {
            color: rgba(107, 114, 128, 0.8);
        }

        .mobile-textarea:focus {
            outline: none;
            border-color: rgba(59, 130, 246, 0.6);
            background: rgba(255, 255, 255, 0.35);
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
        }

        .dark .mobile-textarea {
            background: rgba(31, 41, 55, 0.3);
            color: #f3f4f6;
            border-color: rgba(255, 255, 255, 0.3);
        }

        .dark .mobile-textarea::placeholder {
            color: rgba(156, 163, 175, 0.8);
        }

        .dark .mobile-textarea:focus {
            border-color: rgba(96, 165, 250, 0.6);
            background: rgba(31, 41, 55, 0.4);
            box-shadow: 0 4px 20px rgba(96, 165, 250, 0.2);
        }

        /* 移动端下拉选择器 - 增强毛玻璃效果 */
        .mobile-select {
            appearance: none;
            width: 100%;
            padding: 8px 32px 8px 12px;
            border: 1px solid rgba(255, 255, 255, 0.5);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(25px) saturate(200%);
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 8px center;
            background-size: 14px;
        }

        .mobile-select:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.5);
            border-color: rgba(59, 130, 246, 0.6);
            box-shadow: 0 6px 24px rgba(59, 130, 246, 0.25);
        }

        .dark .mobile-select {
            background: rgba(31, 41, 55, 0.5);
            color: #f3f4f6;
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23d1d5db' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 8px center;
            background-size: 14px;
        }

        .dark .mobile-select:focus {
            background: rgba(31, 41, 55, 0.6);
            border-color: rgba(96, 165, 250, 0.6);
            box-shadow: 0 6px 24px rgba(96, 165, 250, 0.25);
        }

        /* 紧凑型语言选择框 - 特别优化 */
        .mobile-select-compact {
            background: rgba(255, 255, 255, 0.5) !important;
            border: 1px solid rgba(255, 255, 255, 0.6) !important;
            backdrop-filter: blur(30px) saturate(220%) !important;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
            font-weight: 600;
        }

        .mobile-select-compact:focus {
            background: rgba(255, 255, 255, 0.6) !important;
            border-color: rgba(59, 130, 246, 0.7) !important;
            box-shadow: 0 8px 28px rgba(59, 130, 246, 0.3) !important;
        }

        .dark .mobile-select-compact {
            background: rgba(31, 41, 55, 0.6) !important;
            border-color: rgba(255, 255, 255, 0.4) !important;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4) !important;
            color: #f9fafb !important;
        }

        .dark .mobile-select-compact:focus {
            background: rgba(31, 41, 55, 0.7) !important;
            border-color: rgba(96, 165, 250, 0.7) !important;
            box-shadow: 0 8px 28px rgba(96, 165, 250, 0.3) !important;
        }

        /* 下拉框选项样式美化 */
        .mobile-select option {
            background: rgba(255, 255, 255, 0.95);
            color: #374151;
            padding: 12px 16px;
            font-weight: 500;
            border: none;
            backdrop-filter: blur(20px);
        }

        .mobile-select option:hover,
        .mobile-select option:focus {
            background: rgba(59, 130, 246, 0.1);
            color: #1d4ed8;
        }

        .mobile-select option:checked {
            background: rgba(59, 130, 246, 0.2);
            color: #1d4ed8;
            font-weight: 600;
        }

        .dark .mobile-select option {
            background: rgba(31, 41, 55, 0.95);
            color: #f3f4f6;
        }

        .dark .mobile-select option:hover,
        .dark .mobile-select option:focus {
            background: rgba(96, 165, 250, 0.2);
            color: #93c5fd;
        }

        .dark .mobile-select option:checked {
            background: rgba(96, 165, 250, 0.3);
            color: #93c5fd;
            font-weight: 600;
        }

        /* 针对WebKit浏览器的下拉框样式 */
        @supports (-webkit-appearance: none) {
            .mobile-select {
                appearance: none;
                -webkit-appearance: none;
                -moz-appearance: none;
            }

            .mobile-select::-webkit-scrollbar {
                width: 8px;
            }

            .mobile-select::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 4px;
            }

            .mobile-select::-webkit-scrollbar-thumb {
                background: rgba(59, 130, 246, 0.3);
                border-radius: 4px;
            }

            .mobile-select::-webkit-scrollbar-thumb:hover {
                background: rgba(59, 130, 246, 0.5);
            }

            .dark .mobile-select::-webkit-scrollbar-track {
                background: rgba(31, 41, 55, 0.3);
            }

            .dark .mobile-select::-webkit-scrollbar-thumb {
                background: rgba(96, 165, 250, 0.4);
            }

            .dark .mobile-select::-webkit-scrollbar-thumb:hover {
                background: rgba(96, 165, 250, 0.6);
            }
        }

        /* 自定义下拉框组件样式 */
        .custom-select {
            position: relative;
            display: inline-block;
            z-index: 1000;
        }

        /* 模型选择下拉框 - 最高优先级 */
        #model-select {
            z-index: 10000 !important;
        }

        #model-select .custom-select-options {
            z-index: 10001 !important;
        }

        /* 语言选择下拉框 - 中等优先级 */
        #source-language-select,
        #target-language-select {
            z-index: 9000 !important;
        }

        #source-language-select .custom-select-options,
        #target-language-select .custom-select-options {
            z-index: 9001 !important;
        }

        .custom-select-trigger {
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            user-select: none;
            width: 100%;
            padding: 8px 12px;
            border: 1px solid rgba(255, 255, 255, 0.4);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(25px) saturate(200%);
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            transition: all 0.3s ease;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .custom-select-trigger:hover {
            background: rgba(255, 255, 255, 0.35);
            border-color: rgba(59, 130, 246, 0.6);
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
        }

        .dark .custom-select-trigger {
            background: rgba(31, 41, 55, 0.4);
            color: #f9fafb;
            border-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .dark .custom-select-trigger:hover {
            background: rgba(31, 41, 55, 0.5);
            border-color: rgba(96, 165, 250, 0.6);
            box-shadow: 0 4px 20px rgba(96, 165, 250, 0.2);
        }

        .custom-select-options {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            z-index: 99999;
            max-height: 200px;
            overflow-y: auto;
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(35px) saturate(250%);
            border: 1px solid rgba(255, 255, 255, 0.7);
            border-radius: 12px;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
            margin-top: 4px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .custom-select-options.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .custom-select-option {
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 500;
            color: #374151;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        .custom-select-option:last-child {
            border-bottom: none;
        }

        .custom-select-option:hover {
            background: rgba(59, 130, 246, 0.1);
            color: #1d4ed8;
        }

        .custom-select-option.selected {
            background: rgba(59, 130, 246, 0.2);
            color: #1d4ed8;
            font-weight: 600;
        }

        .dark .custom-select-options {
            background: rgba(31, 41, 55, 0.85);
            backdrop-filter: blur(35px) saturate(250%);
            border-color: rgba(255, 255, 255, 0.4);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
        }

        .dark .custom-select-option {
            color: #f3f4f6;
            border-bottom-color: rgba(255, 255, 255, 0.1);
        }

        .dark .custom-select-option:hover {
            background: rgba(96, 165, 250, 0.2);
            color: #93c5fd;
        }

        .dark .custom-select-option.selected {
            background: rgba(96, 165, 250, 0.3);
            color: #93c5fd;
        }

        /* 自定义滚动条 */
        .custom-select-options::-webkit-scrollbar {
            width: 6px;
        }

        .custom-select-options::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 3px;
        }

        .custom-select-options::-webkit-scrollbar-thumb {
            background: rgba(59, 130, 246, 0.3);
            border-radius: 3px;
        }

        .custom-select-options::-webkit-scrollbar-thumb:hover {
            background: rgba(59, 130, 246, 0.5);
        }

        .dark .custom-select-options::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .dark .custom-select-options::-webkit-scrollbar-thumb {
            background: rgba(96, 165, 250, 0.4);
        }

        .dark .custom-select-options::-webkit-scrollbar-thumb:hover {
            background: rgba(96, 165, 250, 0.6);
        }

        /* 加载动画 */
        .loading-dots::after {
            content: '...';
            animation: loading-dots-cycle 1.5s infinite;
        }

        @keyframes loading-dots-cycle {
            0% { content: '.'; }
            33% { content: '..'; }
            66% { content: '...'; }
            100% { content: '.'; }
        }

        /* 玻璃翻译按钮 */
        .glass-translate-button {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px) saturate(180%);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #374151;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .glass-translate-button:hover:not(:disabled) {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(59, 130, 246, 0.4);
            color: #3b82f6;
            transform: translateY(-1px);
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
        }

        .glass-translate-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .dark .glass-translate-button {
            background: rgba(30, 41, 59, 0.3);
            color: #e5e7eb;
            border-color: rgba(255, 255, 255, 0.2);
        }

        .dark .glass-translate-button:hover:not(:disabled) {
            background: rgba(30, 41, 59, 0.4);
            border-color: rgba(96, 165, 250, 0.4);
            color: #60a5fa;
            box-shadow: 0 4px 20px rgba(96, 165, 250, 0.3);
        }

        /* 玻璃交换按钮 */
        .glass-swap-button {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px) saturate(150%);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
        }

        .glass-swap-button:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(59, 130, 246, 0.3);
            transform: scale(1.05);
        }

        .dark .glass-swap-button {
            background: rgba(30, 41, 59, 0.2);
            border-color: rgba(255, 255, 255, 0.1);
        }

        .dark .glass-swap-button:hover {
            background: rgba(30, 41, 59, 0.3);
            border-color: rgba(96, 165, 250, 0.3);
        }

        /* 玻璃清除按钮 */
        .glass-clear-button {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px) saturate(180%);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #6b7280;
            transition: all 0.3s ease;
        }

        .glass-clear-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(239, 68, 68, 0.4);
            color: #ef4444;
            transform: translateY(-1px);
            box-shadow: 0 4px 20px rgba(239, 68, 68, 0.2);
        }

        .dark .glass-clear-button {
            background: rgba(30, 41, 59, 0.3);
            color: #9ca3af;
            border-color: rgba(255, 255, 255, 0.2);
        }

        .dark .glass-clear-button:hover {
            background: rgba(30, 41, 59, 0.4);
            border-color: rgba(239, 68, 68, 0.4);
            color: #f87171;
            box-shadow: 0 4px 20px rgba(239, 68, 68, 0.3);
        }

        /* 玻璃粘贴按钮 */
        .glass-paste-button {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px) saturate(180%);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #6b7280;
            transition: all 0.3s ease;
        }

        .glass-paste-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(34, 197, 94, 0.4);
            color: #22c55e;
            transform: translateY(-1px);
            box-shadow: 0 4px 20px rgba(34, 197, 94, 0.2);
        }

        .dark .glass-paste-button {
            background: rgba(30, 41, 59, 0.3);
            color: #9ca3af;
            border-color: rgba(255, 255, 255, 0.2);
        }

        .dark .glass-paste-button:hover {
            background: rgba(30, 41, 59, 0.4);
            border-color: rgba(34, 197, 94, 0.4);
            color: #4ade80;
            box-shadow: 0 4px 20px rgba(34, 197, 94, 0.3);
        }

        /* 玻璃复制按钮 */
        .glass-copy-button {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px) saturate(180%);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: #6b7280;
            transition: all 0.3s ease;
        }

        .glass-copy-button:hover:not(:disabled) {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(59, 130, 246, 0.4);
            color: #3b82f6;
            transform: translateY(-1px);
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
        }

        .glass-copy-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .dark .glass-copy-button {
            background: rgba(30, 41, 59, 0.3);
            color: #9ca3af;
            border-color: rgba(255, 255, 255, 0.2);
        }

        .dark .glass-copy-button:hover:not(:disabled) {
            background: rgba(30, 41, 59, 0.4);
            border-color: rgba(59, 130, 246, 0.4);
            color: #60a5fa;
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.3);
        }



        /* 旋转动画 */
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .animate-spin {
            animation: spin 1s linear infinite;
        }

        /* 响应式调整 */
        @media (max-width: 480px) {
            .sidebar {
                width: 85%;
                max-width: none;
            }
        }

        /* 防止页面滚动 */
        body.sidebar-open {
            overflow: hidden;
        }

        /* 优化触摸体验 */
        * {
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
        }

        input, textarea {
            -webkit-user-select: text;
            user-select: text;
        }

        /* 拖拽样式 */
        .draggable {
            cursor: grab;
            transition: all 0.3s ease;
            position: relative;
        }

        .draggable:active {
            cursor: grabbing;
        }

        .drag-ready {
            animation: dragReady 0.3s ease-out;
            transform: scale(1.05);
            box-shadow: rgba(0, 0, 0, 0.2) 0px 8px 25px;
            z-index: 999;
        }

        .dragging {
            position: fixed !important;
            z-index: 1000 !important;
            pointer-events: auto;
            opacity: 0.9;
            transform: scale(1.08) rotate(2deg);
            box-shadow: rgba(0, 0, 0, 0.3) 0px 12px 40px,
                        rgba(0, 0, 0, 0.15) 0px -6px 20px inset,
                        rgba(255, 255, 255, 0.1) 0px 2px 8px inset;
            backdrop-filter: blur(8px) contrast(1.15) brightness(1.05) saturate(1.1);
            border-radius: 16px;
            transition: none;
        }

        @keyframes dragReady {
            0% {
                transform: scale(1);
                box-shadow: none;
            }
            50% {
                transform: scale(1.08) translateY(-5px);
                box-shadow: rgba(0, 0, 0, 0.3) 0px 12px 30px;
            }
            100% {
                transform: scale(1.05);
                box-shadow: rgba(0, 0, 0, 0.2) 0px 8px 25px;
            }
        }

        .drag-return {
            transition: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
        }

        .drag-bounce {
            animation: dragBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        @keyframes dragBounce {
            0% { transform: scale(1.08) rotate(2deg); }
            50% { transform: scale(0.95) rotate(-1deg); }
            70% { transform: scale(1.02) rotate(0.5deg); }
            100% { transform: scale(1) rotate(0deg); }
        }

    </style>
</head>

<body>
    <!-- SVG图标定义 -->
    <svg style="display: none;">
        <defs>
            <symbol id="icon-menu" viewBox="0 0 24 24">
                <path d="M3 6h18M3 12h18M3 18h18" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
            </symbol>
            <symbol id="icon-close" viewBox="0 0 24 24">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
            </symbol>
            <symbol id="icon-language" viewBox="0 0 24 24">
                <path d="M5 8l6 6M4 14l6-6-2-3M2 5h12M7 2h1M13 2h1M13 14h7l-3-3M17 17l3-3-3-3" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
            </symbol>
            <symbol id="icon-user" viewBox="0 0 24 24">
                <circle cx="12" cy="8" r="5" stroke="currentColor" stroke-width="1.5" fill="none"/>
                <path d="M20 21a8 8 0 1 0-16 0" stroke="currentColor" stroke-width="1.5" fill="none"/>
            </symbol>
            <symbol id="icon-sun" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="4" stroke="currentColor" stroke-width="1.5" fill="none"/>
                <path d="M12 2v2M12 20v2M4.93 4.93l1.41 1.41M17.66 17.66l1.41 1.41M2 12h2M20 12h2M6.34 6.34L4.93 4.93M19.07 19.07l-1.41-1.41" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
            </symbol>
            <symbol id="icon-moon" viewBox="0 0 24 24">
                <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" stroke="currentColor" stroke-width="1.5" fill="none"/>
            </symbol>
            <symbol id="icon-settings" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="1.5" fill="none"/>
                <path d="M12 1v6M12 17v6M5.64 5.64l4.24 4.24M14.12 14.12l4.24 4.24M1 12h6M17 12h6M5.64 18.36l4.24-4.24M14.12 9.88l4.24-4.24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
            </symbol>
            <symbol id="icon-login" viewBox="0 0 24 24">
                <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4M10 17l5-5-5-5M15 12H3" stroke="currentColor" stroke-width="1.5" fill="none"/>
            </symbol>
            <symbol id="icon-logout" viewBox="0 0 24 24">
                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4M16 17l5-5-5-5M21 12H9" stroke="currentColor" stroke-width="1.5" fill="none"/>
            </symbol>
            <symbol id="icon-history" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5" fill="none"/>
                <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </symbol>
            <symbol id="icon-exchange" viewBox="0 0 24 24">
                <path d="M7 16l4-4-4-4M17 8l4 4-4 4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
                <path d="M11 12h10M3 12h4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
            </symbol>
            <symbol id="icon-copy" viewBox="0 0 24 24">
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="1.5" fill="none"/>
                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1" stroke="currentColor" stroke-width="1.5" fill="none"/>
            </symbol>
            <symbol id="icon-eraser" viewBox="0 0 1024 1024">
                <path d="M981.333333 938.666667H42.666667c-25.6 0-42.666667 17.066667-42.666667 42.666666s17.066667 42.666667 42.666667 42.666667h938.666666c25.6 0 42.666667-17.066667 42.666667-42.666667s-17.066667-42.666667-42.666667-42.666666zM174.933333 840.533333c8.533333 8.533333 21.333333 12.8 34.133334 12.8h341.333333c12.8 0 21.333333-4.266667 29.866667-12.8L1011.2 409.6c8.533333-8.533333 12.8-21.333333 12.8-29.866667s-4.266667-21.333333-12.8-29.866666L674.133333 12.8c-17.066667-17.066667-42.666667-17.066667-59.733333 0L12.8 614.4c-17.066667 17.066667-17.066667 42.666667 0 59.733333l162.133333 166.4z m469.333334-738.133333l273.066666 273.066667-243.2 243.2-268.8-273.066667 238.933334-243.2z m-298.666667 302.933333l273.066667 273.066667-89.6 89.6H221.866667l-123.733334-123.733333 247.466667-238.933334z" fill="currentColor"/>
            </symbol>
            <symbol id="icon-paste" viewBox="0 0 24 24">
                <rect x="8" y="2" width="8" height="4" rx="1" ry="1" stroke="currentColor" stroke-width="1.5" fill="none"/>
                <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" stroke="currentColor" stroke-width="1.5" fill="none"/>
                <path d="M9 12h6M9 16h6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
            </symbol>
            <symbol id="icon-spinner" viewBox="0 0 24 24">
                <path d="M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
            </symbol>
            <symbol id="icon-check" viewBox="0 0 24 24">
                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
            </symbol>
            <symbol id="icon-info" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5" fill="none"/>
                <path d="M12 16v-4M12 8h.01" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
            </symbol>
            <symbol id="icon-warning" viewBox="0 0 24 24">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" stroke="currentColor" stroke-width="1.5" fill="none"/>
                <path d="M12 9v4M12 17h.01" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
            </symbol>
            <symbol id="icon-error" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5" fill="none"/>
                <path d="M15 9l-6 6M9 9l6 6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
            </symbol>
            <symbol id="icon-time" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5" fill="none"/>
                <path d="M12 6v6l4 2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
            </symbol>
            <symbol id="icon-refresh" viewBox="0 0 24 24">
                <path d="M1 4v6h6M23 20v-6h-6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
                <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
            </symbol>
            <symbol id="icon-delete" viewBox="0 0 24 24">
                <path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
                <path d="M10 11v6M14 11v6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
            </symbol>
            <symbol id="icon-search" viewBox="0 0 24 24">
                <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="1.5" fill="none"/>
                <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </symbol>
            <symbol id="icon-expand" viewBox="0 0 24 24">
                <path d="M8 9l4-4 4 4" stroke="currentColor" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
            </symbol>
            <symbol id="icon-collapse" viewBox="0 0 24 24">
                <path d="M16 15l-4 4-4-4" stroke="currentColor" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
            </symbol>
            <symbol id="icon-back" viewBox="0 0 24 24">
                <path d="M19 12H5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
                <path d="M12 19l-7-7 7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
            </symbol>
        </defs>
    </svg>

    <div id="app" v-cloak class="min-h-screen"
        :style="{ background: isDarkMode ? '#000000' : '#7dd3fc' }">



        <!-- 移动端侧边栏遮罩 -->
        <div class="mobile-sidebar-overlay md:hidden" :class="{ active: sidebarOpen }" @click="closeSidebar"></div>

        <!-- 移动端侧边栏 -->
        <div class="mobile-sidebar md:hidden" :class="{ active: sidebarOpen }">
            <div class="flex flex-col h-full">
                <!-- 侧边栏头部 -->
                <div class="flex items-center justify-between h-16 px-4">
                    <h1 class="text-lg font-bold text-gray-800 dark:text-white">菜单</h1>
                    <button @click="closeSidebar" class="p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg">
                        <svg class="icon w-5 h-5">
                            <use href="#icon-close"></use>
                        </svg>
                    </button>
                </div>

                <!-- 用户信息 -->
                <div v-if="isLoggedIn" class="px-4 py-4">
                    <div class="flex items-center space-x-3">
                        <svg class="icon text-gray-600 dark:text-gray-300 w-6 h-6">
                            <use href="#icon-user"></use>
                        </svg>
                        <div>
                            <p class="font-medium text-gray-800 dark:text-white">{{ userInfo?.user_name || '用户' }}</p>
                            <p v-if="isAdmin || isSuperAdmin" class="text-sm text-blue-600 dark:text-blue-400 font-medium">
                                {{ userInfo?.role === 'super_admin' ? '超级管理员' : '管理员' }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 菜单项 -->
                <nav class="flex-1 px-2 py-4 space-y-1">
                    <div @click="toggleDarkMode" class="sidebar-menu-item">
                        <svg :class="isDarkMode ? 'text-yellow-500' : 'text-indigo-500'" class="icon">
                            <use :href="isDarkMode ? '#icon-sun' : '#icon-moon'"></use>
                        </svg>
                        <span>{{ isDarkMode ? '浅色模式' : '深色模式' }}</span>
                    </div>

                    <div @click="toggleModelSelector" class="sidebar-menu-item">
                        <svg class="icon text-green-500">
                            <use href="#icon-settings"></use>
                        </svg>
                        <span>翻译模型</span>
                    </div>

                    <div @click="showTranslationHistory" class="sidebar-menu-item">
                        <svg class="icon text-blue-500">
                            <use href="#icon-history"></use>
                        </svg>
                        <span>翻译历史</span>
                    </div>

                    <a v-if="isAdmin || isSuperAdmin" href="/admin" class="sidebar-menu-item">
                        <svg class="icon text-purple-500">
                            <use href="#icon-settings"></use>
                        </svg>
                        <span>管理面板</span>
                    </a>

                    <a href="/login" v-if="!isLoggedIn" class="sidebar-menu-item">
                        <svg class="icon text-green-500">
                            <use href="#icon-login"></use>
                        </svg>
                        <span>登录</span>
                    </a>

                    <div @click="logout" v-if="isLoggedIn" class="sidebar-menu-item">
                        <svg class="icon text-red-500">
                            <use href="#icon-logout"></use>
                        </svg>
                        <span>退出登录</span>
                    </div>
                </nav>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <main class="min-h-screen">
            <!-- 汉堡菜单按钮 -->
            <button @click="toggleSidebar" class="fixed top-6 left-6 z-40 hamburger text-gray-700 dark:text-gray-200" :class="{ active: sidebarOpen }">
                <span></span>
                <span></span>
                <span></span>
            </button>

            <!-- 翻译界面 -->
            <div class="p-4 pt-24 space-y-6" id="translator" style="position: relative; z-index: 10;">
                <!-- 模型选择区域（动态显示） -->
                <div v-if="showModelSelector" class="glass-effect rounded-2xl p-4" style="position: relative; z-index: 10000;">
                    <div class="flex items-center justify-between mb-3">
                        <label class="text-sm font-medium text-gray-700 dark:text-gray-200">翻译模型</label>
                        <button @click="toggleModelSelector" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                            <svg class="icon w-5 h-5">
                                <use href="#icon-close"></use>
                            </svg>
                        </button>
                    </div>
                    <div class="custom-select w-full" id="model-select">
                        <div @click="toggleModelDropdown" class="custom-select-trigger">
                            <span>{{ models[selectedModel] || '通义千问2 7B' }}</span>
                            <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': showModelDropdown }" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="6,9 12,15 18,9"></polyline>
                            </svg>
                        </div>
                        <div class="custom-select-options" :class="{ show: showModelDropdown }" id="model-options">
                            <div v-for="(desc, model) in models" :key="model"
                                 @click="selectModel(model)" class="custom-select-option" :class="{ selected: selectedModel === model }">
                                {{ desc }}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 语言选择区域 -->
                <div>
                    <div class="flex items-center justify-center space-x-3">
                        <!-- 源语言选择 -->
                        <div class="custom-select min-w-[140px]" id="source-language-select">
                            <div @click="toggleSourceLanguageDropdown" class="custom-select-trigger">
                                <span>{{ sourceLanguage === 'auto' ? '自动检测' : languages[sourceLanguage] || '自动检测' }}</span>
                                <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': showSourceLanguageDropdown }" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="6,9 12,15 18,9"></polyline>
                                </svg>
                            </div>
                            <div class="custom-select-options" :class="{ show: showSourceLanguageDropdown }" id="source-language-options">
                                <div @click="selectSourceLanguage('auto')" class="custom-select-option" :class="{ selected: sourceLanguage === 'auto' }">
                                    自动检测
                                </div>
                                <template v-for="(name, code) in languages" :key="code">
                                    <div v-if="code !== 'auto'" @click="selectSourceLanguage(code)" class="custom-select-option" :class="{ selected: sourceLanguage === code }">
                                        {{ name }}
                                    </div>
                                </template>
                            </div>
                        </div>

                        <!-- 语言交换按钮 -->
                        <button @click="swapLanguages" class="glass-swap-button flex-shrink-0 p-2 text-gray-600 dark:text-gray-300 hover:text-blue-500 dark:hover:text-blue-400 transition-all duration-300">
                            <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M8 3L4 7l4 4M16 21l4-4-4-4"/>
                                <path d="M4 7h16M20 17H4"/>
                            </svg>
                        </button>

                        <!-- 目标语言选择 -->
                        <div class="custom-select min-w-[140px]" id="target-language-select">
                            <div @click="toggleTargetLanguageDropdown" class="custom-select-trigger">
                                <span>{{ languages[targetLanguage] || '英语' }}</span>
                                <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': showTargetLanguageDropdown }" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="6,9 12,15 18,9"></polyline>
                                </svg>
                            </div>
                            <div class="custom-select-options" :class="{ show: showTargetLanguageDropdown }" id="target-language-options">
                                <template v-for="(name, code) in languages" :key="code">
                                    <div v-if="code !== 'auto'" @click="selectTargetLanguage(code)" class="custom-select-option" :class="{ selected: targetLanguage === code }">
                                        {{ name }}
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 源文本区域 -->
                <div class="relative draggable" style="z-index: 1;" @mousedown="startDrag" @touchstart="startDrag">
                    <textarea
                        v-model="sourceText"
                        placeholder="请输入要翻译的文本..."
                        class="mobile-textarea pr-20"
                        :maxlength="maxCharacters"
                        @input="onSourceTextInput"
                        rows="5"
                    ></textarea>
                    <div class="absolute bottom-3 right-3 text-xs pointer-events-none z-10" :class="sourceText.length > maxCharacters * 0.9 ? 'text-red-600 dark:text-red-400' : 'text-gray-500 dark:text-gray-400'">
                        {{ sourceText.length }} / {{ maxCharacters }}
                    </div>
                </div>

                <!-- 翻译、粘贴和清除按钮 -->
                <div class="flex justify-center items-center space-x-4" style="position: relative; z-index: 2;">
                    <button @click="translateText" :disabled="!sourceText.trim() || isTranslating"
                            class="glass-translate-button px-8 py-3 rounded-xl font-medium draggable" @mousedown="startDrag" @touchstart="startDrag">
                        <svg v-if="isTranslating" class="icon animate-spin mr-2">
                            <use href="#icon-spinner"></use>
                        </svg>
                        {{ isTranslating ? '翻译中...' : '翻译' }}
                    </button>
                    <button @click="pasteFromClipboard" class="glass-paste-button p-3 rounded-xl draggable" @mousedown="startDrag" @touchstart="startDrag">
                        <svg class="icon w-5 h-5">
                            <use href="#icon-paste"></use>
                        </svg>
                    </button>
                    <button @click="clearAll" class="glass-clear-button p-3 rounded-xl draggable" @mousedown="startDrag" @touchstart="startDrag">
                        <svg class="icon w-5 h-5">
                            <use href="#icon-eraser"></use>
                        </svg>
                    </button>
                    <button @click="copyTranslatedText" :disabled="!translatedText.trim()" class="glass-copy-button p-3 rounded-xl draggable" @mousedown="startDrag" @touchstart="startDrag">
                        <svg class="icon w-5 h-5">
                            <use href="#icon-copy"></use>
                        </svg>
                    </button>
                </div>

                <!-- 目标文本区域 -->
                <div class="relative draggable" style="z-index: 1;" @mousedown="startDrag" @touchstart="startDrag">
                    <textarea
                        v-model="translatedText"
                        placeholder="翻译结果将在此显示..."
                        class="mobile-textarea"
                        rows="5"
                    ></textarea>

                    <!-- 复制按钮 -->
                    <button
                        v-if="translatedText"
                        @click="copyText(translatedText)"
                        class="absolute top-3 right-3 p-2 bg-blue-500 text-white rounded-lg backdrop-filter backdrop-blur-sm draggable"
                        @mousedown="startDrag" @touchstart="startDrag"
                    >
                        <svg class="icon text-sm">
                            <use href="#icon-copy"></use>
                        </svg>
                    </button>
                </div>




            </div>
        </main>

        <!-- 翻译历史模态框 -->
        <div v-if="showHistoryModal" class="fixed inset-0 bg-black/40 backdrop-blur-sm z-50 flex items-center justify-center p-4" @click.self="showHistoryModal = false">
            <div class="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-2xl shadow-2xl w-full max-w-3xl max-h-[85vh] overflow-hidden">
                <!-- 模态框头部 -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                    <!-- 返回按钮（仅在详情视图显示） -->
                    <div class="flex items-center">
                        <button
                            v-if="expandedHistoryId"
                            @click="expandedHistoryId = null; currentHistoryItem = null"
                            class="p-2 mr-3 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
                            title="返回列表"
                        >
                            <svg class="w-5 h-5 text-gray-600 dark:text-gray-400">
                                <use href="#icon-back"></use>
                            </svg>
                        </button>
                        <h2 class="text-xl font-bold text-gray-900 dark:text-white">
                            {{ expandedHistoryId ? '翻译详情' : '翻译历史' }}
                        </h2>
                    </div>
                    <button @click="showHistoryModal = false" class="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
                        <svg class="w-5 h-5 text-gray-600 dark:text-gray-400">
                            <use href="#icon-close"></use>
                        </svg>
                    </button>
                </div>

                <!-- 内容区域 - 历史列表页面 -->
                <div class="relative overflow-hidden" style="height: calc(85vh - 140px);">
                    <!-- 历史列表视图 -->
                    <div class="absolute inset-0">
                        <!-- 搜索栏 -->
                        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="w-4 h-4 text-gray-500 dark:text-gray-400">
                                        <use href="#icon-search"></use>
                                    </svg>
                                </div>
                                <input
                                    v-model="historySearch"
                                    @input="searchHistory"
                                    @keyup.enter="loadTranslationHistory(1)"
                                    type="text"
                                    placeholder="搜索翻译内容、语言对或日期..."
                                    class="w-full pl-10 pr-4 py-2.5 bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg text-sm text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                                >
                            </div>
                        </div>

                        <!-- 历史记录列表 -->
                        <div class="flex-1 overflow-y-auto" style="height: calc(100% - 80px);">
                    <!-- 加载状态 -->
                    <div v-if="historyLoading" class="flex items-center justify-center py-12">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <span class="ml-2 text-gray-700 dark:text-gray-300">加载中...</span>
                    </div>

                    <!-- 空状态 -->
                    <div v-else-if="translationHistory.length === 0" class="flex flex-col items-center justify-center py-12 text-gray-600 dark:text-gray-400">
                        <svg class="w-16 h-16 mb-4 opacity-60">
                            <use href="#icon-history"></use>
                        </svg>
                        <p class="text-lg font-medium">暂无翻译历史</p>
                        <p class="text-sm opacity-80">开始翻译后，历史记录将显示在这里</p>
                    </div>

                    <!-- 历史记录项 -->
                    <div v-else class="space-y-4 p-4">
                        <div
                            v-for="(item, index) in translationHistory"
                            :key="item.id"
                            class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden cursor-pointer"
                            @click="toggleHistoryExpansion(item.id)"
                        >
                            <!-- 卡片头部 -->
                            <div class="relative p-4 pb-3">
                                <!-- 语言标签徽章 -->
                                <div class="absolute top-3 right-3">
                                    <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 border border-blue-200 dark:border-blue-700/50">
                                        <span v-html="highlightSearchText(item.source_language_name + ' → ' + item.target_language_name)"></span>
                                    </span>
                                </div>

                                <!-- 时间信息 -->
                                <div class="flex items-center text-xs text-gray-500 dark:text-gray-400 mb-3 pr-20">
                                    <svg class="w-3.5 h-3.5 mr-1">
                                        <use href="#icon-time"></use>
                                    </svg>
                                    <span v-html="highlightSearchText(formatTime(item.created_at))"></span>
                                </div>
                            </div>

                            <!-- 翻译内容 -->
                            <div class="px-4 pb-4 space-y-3">
                                <!-- 原文 -->
                                <div>
                                    <div class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">Original Text</div>
                                    <div class="text-sm font-semibold text-gray-900 dark:text-gray-100 leading-relaxed line-clamp-3" v-html="highlightSearchText(item.source_text)"></div>
                                </div>

                                <!-- 分隔线 -->
                                <div class="border-t border-gray-200/50 dark:border-gray-700/50 my-3"></div>

                                <!-- 译文 -->
                                <div class="relative">
                                    <div class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2">Translation</div>
                                    <div class="text-sm text-gray-800 dark:text-gray-200 leading-relaxed line-clamp-3" v-html="highlightSearchText(item.translated_text)"></div>
                                </div>
                            </div>

                            <!-- 卡片底部操作区 -->
                            <div class="px-4 py-3 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                                <div class="flex items-center justify-between">
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ item.source_text.length }} 字符
                                        <span v-if="item.translation_time" class="ml-2">· {{ item.translation_time }}秒</span>
                                        <span class="ml-2">· 点击查看详情</span>
                                    </div>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-gray-400 dark:text-gray-500">
                                            <path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    </div>
                </div>

                <!-- 分页控件 -->
                <div v-if="historyPagination.pages > 1 && !expandedHistoryId" class="sticky bottom-0 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700 dark:text-gray-300">
                            共 {{ historyPagination.total }} 条记录，第 {{ historyPagination.page }} / {{ historyPagination.pages }} 页
                        </div>
                        <div class="flex items-center space-x-2">
                            <button
                                @click="goToHistoryPage(historyPagination.page - 1)"
                                :disabled="!historyPagination.has_prev"
                                class="px-3 py-1 text-sm bg-gray-200 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors text-gray-800 dark:text-gray-200"
                            >
                                上一页
                            </button>
                            <button
                                @click="goToHistoryPage(historyPagination.page + 1)"
                                :disabled="!historyPagination.has_next"
                                class="px-3 py-1 text-sm bg-gray-200 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors text-gray-800 dark:text-gray-200"
                            >
                                下一页
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 翻译详情页面模态框 - 滑动效果 -->
        <div v-show="expandedHistoryId" class="fixed inset-0 z-[60]">
            <!-- 背景遮罩 -->
            <div class="absolute inset-0 bg-black bg-opacity-50 transition-opacity duration-300"
                 :class="expandedHistoryId ? 'opacity-100' : 'opacity-0'"></div>

            <!-- 详情页面容器 - 从右侧滑入 -->
            <div class="absolute inset-0 flex items-center justify-center p-4">
                <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-xl w-full max-w-2xl max-h-[85vh] overflow-hidden transform transition-all duration-500 ease-out"
                     :class="expandedHistoryId ? 'translate-x-0 opacity-100 scale-100' : 'translate-x-full opacity-0 scale-95'">
                <!-- 详情页面标题栏 -->
                <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">翻译详情</h2>
                    <button @click="expandedHistoryId = null; currentHistoryItem = null"
                            class="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
                        <svg class="w-5 h-5 text-gray-600 dark:text-gray-400">
                            <use href="#icon-close"></use>
                        </svg>
                    </button>
                </div>

                <!-- 详情页面内容 -->
                <div class="p-6 overflow-y-auto" style="max-height: calc(85vh - 140px);">
                    <div v-if="currentHistoryItem" class="space-y-6">
                        <!-- 基本信息 -->
                        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                            <h3 class="font-semibold text-gray-900 dark:text-white mb-2">翻译信息</h3>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-600 dark:text-gray-400">模型:</span>
                                    <span class="ml-2 text-gray-900 dark:text-white">{{ currentHistoryItem.model_used }}</span>
                                </div>
                                <div>
                                    <span class="text-gray-600 dark:text-gray-400">时间:</span>
                                    <span class="ml-2 text-gray-900 dark:text-white">{{ formatDate(currentHistoryItem.created_at) }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- 原文 -->
                        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="font-semibold text-gray-900 dark:text-white">原文</h3>
                                <button @click="copyHistoryText(currentHistoryItem.source_text, 'source')"
                                        class="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded text-sm hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors">
                                    复制
                                </button>
                            </div>
                            <p class="text-gray-800 dark:text-gray-200 whitespace-pre-wrap">{{ currentHistoryItem.source_text }}</p>
                        </div>

                        <!-- 译文 -->
                        <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="font-semibold text-gray-900 dark:text-white">译文</h3>
                                <button @click="copyHistoryText(currentHistoryItem.translated_text, 'result')"
                                        class="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded text-sm hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors">
                                    复制
                                </button>
                            </div>
                            <p class="text-gray-800 dark:text-gray-200 whitespace-pre-wrap">{{ currentHistoryItem.translated_text }}</p>
                        </div>
                    </div>

                    <!-- 无数据状态 -->
                    <div v-else class="text-center py-8">
                        <p class="text-gray-500 dark:text-gray-400">无法加载详情数据</p>
                    </div>
                </div>
            </div>
        </div>

    </div> <!-- 结束 Vue 应用容器 #app -->

</body>
</html>

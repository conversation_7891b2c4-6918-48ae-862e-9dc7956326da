#!/usr/bin/env python3
"""
数据库初始化脚本 - 设置超级管理员
使用方法：python init_admin.py --user_number 2406151055 --username 孟雨晨
"""

import os
import sys
import argparse
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from flask import Flask
from src.models.db import db
from src.models.user import User
from src.models.admin_log import AdminLog

def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    app.config['SECRET_KEY'] = os.getenv('FLASK_SECRET_KEY', 'asdf#FGSgvasgf$5$WGT')
    
    # 数据库配置
    db_dir = os.path.join(os.path.dirname(__file__), 'database')
    os.makedirs(db_dir, exist_ok=True)
    app.config['SQLALCHEMY_DATABASE_URI'] = f"sqlite:///{os.path.join(db_dir, 'app.db')}"
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    db.init_app(app)
    return app

def init_super_admin(user_number, username):
    """初始化超级管理员"""
    app = create_app()
    
    with app.app_context():
        # 确保数据库表存在
        db.create_all()
        
        # 查找用户
        user = User.query.filter_by(user_number=user_number).first()
        
        if not user:
            # 如果用户不存在，创建用户
            user = User(
                user_number=user_number,
                username=username,
                role='super_admin'
            )
            db.session.add(user)
            print(f"创建新用户: {username} ({user_number}) 并设置为超级管理员")
        else:
            # 如果用户存在，更新角色
            old_role = user.role
            user.role = 'super_admin'
            user.username = username  # 更新姓名（如果有变化）
            print(f"用户 {username} ({user_number}) 角色从 {old_role} 更新为 super_admin")
        
        # 记录操作日志
        log = AdminLog.log_action(
            operator_user_number='SYSTEM',
            operator_username='系统初始化',
            operator_role='system',
            action='init_super_admin',
            description=f'系统初始化：设置 {username} ({user_number}) 为超级管理员',
            target_user_number=user_number,
            target_username=username,
            details=f'通过初始化脚本设置超级管理员权限',
            status='success'
        )
        
        try:
            db.session.commit()
            print("✅ 超级管理员设置成功！")
            print(f"用户信息：")
            print(f"  学号: {user.user_number}")
            print(f"  姓名: {user.username}")
            print(f"  角色: {user.role}")
            print(f"  用户ID: {user.id}")
            return True
        except Exception as e:
            db.session.rollback()
            print(f"❌ 设置失败: {e}")
            return False

def list_admins():
    """列出所有管理员"""
    app = create_app()
    
    with app.app_context():
        admins = User.query.filter(User.role.in_(['admin', 'super_admin'])).all()
        
        if not admins:
            print("当前没有管理员用户")
            return
        
        print("当前管理员列表：")
        print("-" * 60)
        print(f"{'ID':<5} {'学号':<15} {'姓名':<15} {'角色':<15}")
        print("-" * 60)
        
        for admin in admins:
            role_text = '超级管理员' if admin.role == 'super_admin' else '管理员'
            print(f"{admin.id:<5} {admin.user_number:<15} {admin.username:<15} {role_text:<15}")

def main():
    parser = argparse.ArgumentParser(description='数据库初始化脚本 - 设置超级管理员')
    parser.add_argument('--user_number', type=str, help='用户学号')
    parser.add_argument('--username', type=str, help='用户姓名')
    parser.add_argument('--list', action='store_true', help='列出所有管理员')
    
    args = parser.parse_args()
    
    if args.list:
        list_admins()
        return
    
    if not args.user_number or not args.username:
        print("错误：必须提供用户学号和姓名")
        print("使用方法：")
        print("  设置超级管理员: python init_admin.py --user_number 2406151055 --username 孟雨晨")
        print("  列出管理员: python init_admin.py --list")
        sys.exit(1)
    
    # 确认操作
    print(f"即将设置用户为超级管理员：")
    print(f"  学号: {args.user_number}")
    print(f"  姓名: {args.username}")
    
    confirm = input("确认执行？(y/N): ").strip().lower()
    if confirm != 'y':
        print("操作已取消")
        sys.exit(0)
    
    success = init_super_admin(args.user_number, args.username)
    if success:
        print("\n可以使用以下命令查看管理员列表：")
        print("python init_admin.py --list")
    else:
        sys.exit(1)

if __name__ == '__main__':
    main()

"""
翻译历史记录模型
"""

from datetime import datetime
import pytz
from .db import db

class TranslationHistory(db.Model):
    """翻译历史记录"""
    __tablename__ = 'translation_history'

    id = db.Column(db.Integer, primary_key=True)
    user_number = db.Column(db.String(100), nullable=True, index=True)  # 用户学号
    user_name = db.Column(db.String(100), nullable=True)  # 用户名
    user_section = db.Column(db.String(100), nullable=True)  # 学院信息
    source_text = db.Column(db.Text, nullable=False)  # 原文
    translated_text = db.Column(db.Text, nullable=False)  # 译文
    source_language = db.Column(db.String(10), nullable=False)  # 源语言
    target_language = db.Column(db.String(10), nullable=False)  # 目标语言
    model_used = db.Column(db.String(50), nullable=False)  # 使用的模型
    character_count = db.Column(db.Integer, nullable=False)  # 字符数量
    translation_time = db.Column(db.Float, nullable=True)  # 翻译耗时(秒)
    created_at = db.Column(db.DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Shanghai')))  # 创建时间
    ip_address = db.Column(db.String(45), nullable=True)  # IP地址
    user_agent = db.Column(db.String(500), nullable=True)  # 用户代理

    # 为常用查询添加索引
    __table_args__ = (
        db.Index('idx_history_user_number', 'user_number'),
    )
    
    def __repr__(self):
        return f'<TranslationHistory {self.id}: {self.user_name}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_number': self.user_number,
            'user_name': self.user_name,
            'user_section': self.user_section,
            'source_text': self.source_text,
            'translated_text': self.translated_text,
            'source_language': self.source_language,
            'target_language': self.target_language,
            'model_used': self.model_used,
            'character_count': self.character_count,
            'translation_time': self.translation_time,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent
        }
    
    @classmethod
    def log_translation(cls, user_number, user_name, source_text, translated_text,
                       source_language, target_language, model_used, character_count=None,
                       translation_time=None, ip_address=None, user_agent=None, user_section=None):
        """记录翻译历史"""
        history = cls(
            user_number=user_number,
            user_name=user_name,
            user_section=user_section,
            source_text=source_text,
            translated_text=translated_text,
            source_language=source_language,
            target_language=target_language,
            model_used=model_used,
            character_count=character_count or len(source_text),
            translation_time=translation_time,
            ip_address=ip_address,
            user_agent=user_agent
        )
        db.session.add(history)
        return history

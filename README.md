# 河南农业大学智能翻译平台

一个基于 Flask 和 Vue.js 的智能翻译平台，支持多语言翻译、用户管理和管理员控制台。

## 功能特性

- **多语言翻译**: 支持中文、英语、日语、韩语、法语、德语、西班牙语、俄语、印地语等语言
- **AI 模型支持**: 集成多种本地 LLM 模型进行翻译
- **用户认证**: 支持河南农业大学 OAuth 登录系统
- **管理员系统**: 完整的用户管理、翻译历史查看、黑名单管理
- **响应式设计**: 支持深色/浅色主题切换
- **实时翻译**: 支持流式和同步翻译模式

## 技术栈

### 后端

- **Flask**: Web 框架
- **SQLAlchemy**: ORM 数据库操作
- **JWT**: 用户认证
- **Flask-CORS**: 跨域支持

### 前端

- **Vue.js 3**: 前端框架
- **Tailwind CSS**: 样式框架
- **Chart.js**: 数据可视化

### 数据库

- **SQLite**: 本地数据库存储

## 安装部署

### 环境要求

- Python 3.10+
- uv (推荐) 或 pip

### 快速开始

1. **克隆项目**

```bash
git clone https://git.henau.edu.cn/Chen/llmtran.git
cd Henau_Tra_Model
```

2. **安装依赖**

```bash
# 使用uv (推荐)
uv sync
可以直接uv run main.py自动安装依赖

# 或使用pip
pip install -r requirements.txt
```

3. **环境配置**
   创建 `.env` 文件：

```env
FLASK_SECRET_KEY=your_secret_key_here
HENAU_APPID=your_henau_appid
HENAU_SECRET=your_henau_secret
REDIRECT_URI=http://localhost:5000/api/auth/callback
```

4. **初始化数据库**

```bash
python main.py
```

5. **创建超级管理员**

```bash
python init_admin.py --user_number 学号 --username 姓名
```

6. **启动服务**

```bash
python main.py
```

访问 http://localhost:5000 即可使用。

## 项目结构

```
Henau_Tra_Model/
├── main.py                 # 应用入口
├── init_admin.py          # 管理员初始化脚本
├── pyproject.toml         # 项目配置
├── src/                   # 源代码目录
│   ├── models/           # 数据模型
│   │   ├── user.py       # 用户模型
│   │   ├── translation_history.py  # 翻译历史
│   │   ├── login_history.py        # 登录历史
│   │   ├── blacklist.py            # 黑名单
│   │   └── admin_log.py            # 管理员日志
│   ├── routes/           # 路由处理
│   │   ├── auth.py       # 认证路由
│   │   ├── translation.py # 翻译路由
│   │   └── admin.py      # 管理员路由
│   └── utils/            # 工具函数
│       ├── token_auth.py # JWT认证
│       └── admin_auth.py # 管理员认证
├── static/               # 静态文件
│   ├── index.html        # 主页面
│   ├── admin.html        # 管理员页面
│   ├── login.html        # 登录页面
│   └── *.css, *.js       # 样式和脚本
└── database/             # 数据库文件
    └── app.db
```

## 使用说明

### 普通用户

1. 访问主页，使用河南农业大学统一身份认证账号扫码登录
2. 在翻译界面输入文本，选择源语言和目标语言
3. 选择翻译模型，点击翻译按钮
4. 查看翻译结果和历史记录

### 管理员

1. 使用管理员账号登录后访问 `/admin`
2. 查看用户统计、翻译统计等数据
3. 管理用户权限、查看翻译历史
4. 管理黑名单用户

## 权限说明

- **user**: 普通用户，可使用翻译功能
- **admin**: 管理员，可管理普通用户
- **super_admin**: 超级管理员，拥有所有权限

## API 接口

### 认证相关

- `GET /api/auth/login` - 获取登录二维码
- `GET /api/auth/callback` - OAuth 回调处理
- `GET /api/auth/check` - 检查登录状态
- `POST /api/auth/logout` - 用户登出

### 翻译相关

- `POST /api/translate` - 文本翻译
- `GET /api/models` - 获取可用模型
- `POST /api/detect-language` - 语言检测

### 管理员相关

- `GET /admin/dashboard` - 管理员仪表板
- `GET /admin/users` - 用户列表
- `POST /admin/users` - 创建用户
- `PUT /admin/users/{id}` - 更新用户

## 联系方式

如有问题或建议，请联系项目维护者
WX:AYMYC115

## Plan

1. 实现上传文档 OCR 或者 PDF 之类的文档解析提取后进行翻译
2. 桌面端重定向问题

import os
import json
import requests
import time
import re
from datetime import datetime
from flask import Blueprint, request, jsonify, session, Response
from src.models.db import db
from src.models.login_history import LoginHistory
from src.models.translation_history import TranslationHistory
from src.utils.token_auth import require_auth
from sqlalchemy import or_

translation_bp = Blueprint('translation', __name__)

HENAU_APPID = os.getenv('HENAU_APPID', 'your_app_id')
HENAU_SECRET = os.getenv('HENAU_SECRET', 'your_app_secret')

# 字符限制
MAX_CHARACTERS = 5000

# 可用模型列表 - 基于用户提供的本地模型
AVAILABLE_MODELS = {
    # 推理模型 (带think功能)
    'deepseek-r1:14b': 'DeepSeek R1 14B ',

    # 非推理模型
    'qwen2.5:14b': '通义千问2.5 14B',  # 默认模型
    'qwen2:7b': '通义千问2 7B',
    'qwen2.5:32b': '通义千问2.5 32B',
    'gemma3:27b': 'Gemma 3 27B',
}

# 推理模型列表 (需要移除think内容)
REASONING_MODELS = {
    'deepseek-r1:14b'
}

# 扩展语言映射 - 支持13种语言，繁体中文、简体中文、英语排前面
LANGUAGE_MAP = {
    'auto': '自动检测',
    'zh-tw': '繁体中文',
    'zh': '简体中文',
    'en': '英语',
    'ja': '日语',
    'ko': '韩语',
    'fr': '法语',
    'de': '德语',
    'es': '西班牙语',
    'ru': '俄语',
    'it': '意大利语',
    'pt': '葡萄牙语',
    'ar': '阿拉伯语',
    'hi': '印地语'
}

# Google Translate 语言代码映射
GOOGLE_LANG_MAP = {
    'zh-tw': 'zh-tw',
    'zh': 'zh-cn',
    'en': 'en',
    'ja': 'ja',
    'ko': 'ko',
    'fr': 'fr',
    'de': 'de',
    'es': 'es',
    'ru': 'ru',
    'it': 'it',
    'pt': 'pt',
    'ar': 'ar',
    'hi': 'hi'
}

def check_login():
    # 检查是否有用户信息或access_token - 强制要求真实登录
    return (session.get('user_info') is not None or 
            session.get('access_token') is not None or 
            session.get('henau_openid') is not None)

@translation_bp.route('/models', methods=['GET'])
def get_models():
    """获取可用模型列表"""
    # 使用有序字典保持语言顺序
    from collections import OrderedDict
    ordered_languages = OrderedDict([
        ('auto', '自动检测'),
        ('zh-tw', '繁体中文'),
        ('zh', '简体中文'),
        ('en', '英语'),
        ('ja', '日语'),
        ('ko', '韩语'),
        ('fr', '法语'),
        ('de', '德语'),
        ('es', '西班牙语'),
        ('ru', '俄语'),
        ('it', '意大利语'),
        ('pt', '葡萄牙语'),
        ('ar', '阿拉伯语'),
        ('hi', '印地语')
    ])

    return jsonify({
        'status': 'success',
        'models': AVAILABLE_MODELS,
        'languages': ordered_languages
    })

def remove_think_content(text):
    """移除推理模型的think内容"""
    if not text:
        return text
    
    print(f"Original text before cleaning: {text[:200]}...")  # Debug log
    
    # 移除 <think>...</think> 标签及其内容（多种变体）
    text = re.sub(r'<think>.*?</think>', '', text, flags=re.DOTALL | re.IGNORECASE)
    text = re.sub(r'<thinking>.*?</thinking>', '', text, flags=re.DOTALL | re.IGNORECASE)
    text = re.sub(r'\\u003cthink\\u003e.*?\\u003c/think\\u003e', '', text, flags=re.DOTALL | re.IGNORECASE)
    
    # 移除单独的标签
    text = re.sub(r'</?think>', '', text, flags=re.IGNORECASE)
    text = re.sub(r'</?thinking>', '', text, flags=re.IGNORECASE)
    text = re.sub(r'\\u003c/?think\\u003e', '', text, flags=re.IGNORECASE)
    
    # 移除多余的空行和空格
    text = re.sub(r'\n\s*\n+', '\n', text)
    text = re.sub(r'^\s+', '', text, flags=re.MULTILINE)
    
    cleaned_text = text.strip()
    print(f"Cleaned text: {cleaned_text[:200]}...")  # Debug log
    
    return cleaned_text

def save_translation_history(user_info, source_text, translated_text, source_lang, target_lang, model, translation_time, request_obj):
    """保存翻译历史记录"""
    try:
        history = TranslationHistory(
            user_number=user_info.get('user_number', 'anonymous'),
            user_name=user_info.get('user_name', 'Anonymous'),
            user_section=user_info.get('user_section'),  # 学院信息
            source_text=source_text,
            translated_text=translated_text,
            source_language=source_lang,
            target_language=target_lang,
            model_used=model,
            character_count=len(source_text),
            translation_time=translation_time,
            ip_address=request_obj.remote_addr,
            user_agent=request_obj.headers.get('User-Agent', '')
        )
        db.session.add(history)
        db.session.commit()
    except Exception as e:
        print(f"保存翻译历史失败: {str(e)}")
        db.session.rollback()

# Google Translate function removed - using local models only

@translation_bp.route('/translate', methods=['POST'])
def translate():
    """翻译接口"""
    login_status = check_login()
    if not login_status:
        return jsonify({'status': 'error', 'message': '用户未登录，请先登录或使用演示模式'}), 401
        
    data = request.json or {}
    text = data.get('text', '').strip()
    if not text:
        return jsonify({'status': 'error', 'message': '待翻译文本不能为空'}), 400
    
    # 字符数限制检查
    if len(text) > MAX_CHARACTERS:
        return jsonify({
            'status': 'error', 
            'message': f'文本长度超过限制，最多支持{MAX_CHARACTERS}个字符，当前{len(text)}个字符'
        }), 400
        
    source_lang = data.get('source_lang', 'auto')
    target_lang = data.get('target_lang', 'zh')
    model = data.get('model', 'qwen2:7b')
    stream = data.get('stream', False)
    
    # 验证模型
    if model not in AVAILABLE_MODELS:
        model = 'qwen2:7b'  # 默认使用最小非推理模型
    
    start_time = time.time()
    
    try:
        # 获取用户信息 - 仅支持真实登录用户
        user_info = {
            'user_number': session.get('user_info', {}).get('user_number', 'anonymous'),
            'user_name': session.get('user_info', {}).get('user_name', 'Anonymous')
        }
        
        # 使用本地LLM模型进行翻译
        if stream:
            return translate_stream_enhanced(text, source_lang, target_lang, model, user_info)
        else:
            return translate_sync_enhanced(text, source_lang, target_lang, model, user_info, start_time)
                
    except Exception as e:
        return jsonify({'status': 'error', 'message': f'翻译过程中发生错误: {str(e)}'}), 500

def translate_sync_enhanced(text, source_lang, target_lang, model, user_info, start_time):
    """增强的同步翻译"""
    # 构建翻译提示词
    if source_lang == 'auto':
        prompt = f"请将以下文本翻译成{LANGUAGE_MAP.get(target_lang, target_lang)}，保持原文的语气和风格，只返回翻译结果：\n\n{text}"
    else:
        source_lang_name = LANGUAGE_MAP.get(source_lang, source_lang)
        target_lang_name = LANGUAGE_MAP.get(target_lang, target_lang)
        prompt = f"请将以下{source_lang_name}文本翻译成{target_lang_name}，保持原文的语气和风格，只返回翻译结果：\n\n{text}"

    # 初始化变量
    response_data = None
    status_code = 500

    try:
        result = translate_sync_original(prompt, model, text)
        print(f"Original result type: {type(result)}")  # Debug

        # 处理不同的返回类型
        if isinstance(result, tuple) and len(result) == 2:
            response, status_code = result
            if status_code == 200:
                try:
                    response_data = response.get_json()
                except Exception as parse_error:
                    print(f"Failed to parse response JSON: {parse_error}")
                    response_data = result[0] if isinstance(result[0], dict) else None
            else:
                # 处理非200状态码的情况
                print(f"Received non-200 status code: {status_code}")
                return jsonify({'status': 'error', 'message': f'翻译服务返回错误状态码: {status_code}'}), status_code

        elif hasattr(result, 'get_json'):
            try:
                response_data = result.get_json()
                status_code = 200
            except Exception as parse_error:
                print(f"Failed to parse response JSON: {parse_error}")
                response_data = None
                status_code = 500

        elif isinstance(result, dict):
            response_data = result
            status_code = 200
        else:
            # 直接返回原始结果
            return result

        # 检查response_data是否有效
        if response_data and response_data.get('status') == 'success':
            # 移除think内容（仅对推理模型）
            translated_text = response_data.get('translation', '')
            print(f"Model: {model}, Is reasoning: {model in REASONING_MODELS}")  # Debug
            print(f"Original translation: {translated_text[:200]}...")  # Debug

            if model in REASONING_MODELS:
                cleaned_text = remove_think_content(translated_text)
            else:
                cleaned_text = translated_text

            response_data['translation'] = cleaned_text
            response_data['translated_text'] = cleaned_text

            # 保存翻译历史
            translation_time = round(time.time() - start_time, 2)
            save_translation_history(user_info, text, cleaned_text, source_lang, target_lang, model, translation_time, request)

            response_data['character_count'] = len(text)
            response_data['translation_time'] = round(translation_time, 2)

            return jsonify(response_data)

        # 如果response_data无效，返回错误
        if response_data:
            return jsonify(response_data), status_code
        else:
            return jsonify({'status': 'error', 'message': '翻译服务返回无效响应'}), 500

    except Exception as e:
        print(f"Error in translate_sync_enhanced: {str(e)}")  # Debug
        return jsonify({'status': 'error', 'message': f'翻译失败: {str(e)}'}), 500

def translate_stream_enhanced(text, source_lang, target_lang, model, user_info):
    """增强的流式翻译"""
    # 构建翻译提示词
    if source_lang == 'auto':
        prompt = f"请将以下文本翻译成{LANGUAGE_MAP.get(target_lang, target_lang)}，保持原文的语气和风格，只返回翻译结果：\n\n{text}"
    else:
        source_lang_name = LANGUAGE_MAP.get(source_lang, source_lang)
        target_lang_name = LANGUAGE_MAP.get(target_lang, target_lang)
        prompt = f"请将以下{source_lang_name}文本翻译成{target_lang_name}，保持原文的语气和风格，只返回翻译结果：\n\n{text}"
    
    return translate_stream_original(prompt, model)

def translate_sync_original(prompt, model, text=''):
    """同步翻译"""
    api_url = 'https://oauth.henau.edu.cn/oauth2_ai_server/llm_completions'
    
    # 尝试多种认证方式组合
    user_access_token = session.get('access_token')
    
    # 方式1: 同时使用access_token和appid/secret
    if user_access_token:
        params = {
            'access_token': user_access_token,
            'appid': HENAU_APPID,
            'secret': HENAU_SECRET,
            'style': 'ollama',
            'endpoint': 'v1'
        }
    else:
        params = {
            'appid': HENAU_APPID,
            'secret': HENAU_SECRET,
            'style': 'ollama',
            'endpoint': 'v1',
            'permission': 'henauapi_llm_completions'
        }
    
    payload = {
        'model': model,
        'messages': [{
            'role': 'user',
            'content': prompt
        }],
        'stream': False
    }
    
    try:

        
        response = requests.post(api_url, params=params, json=payload, timeout=30)
        

        
        # 如果是400错误，打印更详细的错误信息
        if response.status_code == 400:
            pass

        
        if response.status_code != 200:

            return jsonify({
                'status': 'error',
                'message': f'API调用失败: HTTP {response.status_code} - {response.text}',
                'details': {
                    'status_code': response.status_code,
                    'reason': response.reason,
                    'response': response.text
                }
            }), response.status_code
            
        result = response.json()
        
        # 检查API返回的错误状态
        if result.get('status') == 'error':
            error_msg = result.get('data', '未知错误')

            
            # 如果是App信息参数错误，尝试不同的参数组合
            if 'App信息参数错误' in error_msg:
                pass

            
            return jsonify({
                'status': 'error',
                'message': f'翻译API错误: {error_msg}',
                'details': result,
                'suggestions': [
                    '请检查AppID/AppSecret是否正确',
                    '请确认接口权限已开通',
                    '请联系平台管理员检查配置'
                ]
            }), 400
        
        # 处理成功的API返回格式 - 支持OpenAI兼容格式
        translated_text = None
        
        # 尝试解析OpenAI兼容格式: choices[0].message.content
        if 'choices' in result and len(result['choices']) > 0:
            choice = result['choices'][0]
            if 'message' in choice and 'content' in choice['message']:
                translated_text = choice['message']['content'].strip()
        # 兼容旧格式: message.content
        elif 'message' in result and 'content' in result['message']:
            translated_text = result['message']['content'].strip()
        
        if translated_text:
            return jsonify({
                'status': 'success',
                'translation': translated_text,
                'translated_text': translated_text,
                'model': result.get('model', model)
            })
        # 如果API返回格式不符合预期，返回错误
        else:
            print(f"API返回格式异常: {result}")
            return jsonify({
                'status': 'error',
                'message': 'API返回格式异常',
                'details': result
            }), 500
            
    except requests.exceptions.RequestException as e:
        print(f"网络请求异常: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'网络请求失败: {str(e)}',
            'details': {'error_type': 'NetworkError', 'error': str(e)}
        }), 500
    except json.JSONDecodeError as e:
        print(f"JSON解析异常: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'API返回数据格式错误: {str(e)}',
            'details': {'error_type': 'JSONDecodeError', 'error': str(e)}
        }), 500
    except Exception as e:
        print(f"未知异常: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': f'翻译过程中发生错误: {str(e)}',
            'details': {'error_type': 'UnknownError', 'error': str(e)}
        }), 500

def translate_stream_original(prompt, model):
    """流式翻译"""
    def generate():
        api_url = 'https://oauth.henau.edu.cn/oauth2_ai_server/llm_completions'
        params = {
            'appid': HENAU_APPID,
            'secret': HENAU_SECRET,
            'style': 'ollama',
            'endpoint': 'v1'
        }
        
        payload = {
            'model': model,
            'messages': [{
                'role': 'user',
                'content': prompt
            }],
            'stream': True
        }
        
        try:
            with requests.post(api_url, params=params, json=payload, stream=True, timeout=30) as response:
                if response.status_code != 200:
                    yield f"data: {json.dumps({'status': 'error', 'message': f'API调用失败，状态码: {response.status_code}'}, ensure_ascii=False)}\n\n"
                    return
                    
                for line in response.iter_lines():
                    if line:
                        try:
                            yield f"data: {line.decode('utf-8')}\n\n"
                        except UnicodeDecodeError:
                            continue
                            
        except requests.exceptions.RequestException as e:
            yield f"data: {json.dumps({'status': 'error', 'message': f'网络请求失败: {str(e)}'}, ensure_ascii=False)}\n\n"
    
    return Response(generate(), mimetype='text/event-stream')


# -------------------- 用户翻译历史管理 --------------------

@translation_bp.route('/user/history', methods=['GET'])
@require_auth
def get_user_translation_history():
    """获取当前用户的翻译历史记录"""
    try:
        # 获取当前用户信息
        user_info = session.get('user_info')
        if not user_info:
            return jsonify({'status': 'error', 'message': '用户未登录'}), 401

        user_number = user_info.get('user_number')
        if not user_number:
            return jsonify({'status': 'error', 'message': '用户信息不完整'}), 400

        # 获取分页参数
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 10, type=int), 50)  # 限制最大50条

        # 获取搜索和筛选参数
        search = request.args.get('search', '').strip()
        source_lang = request.args.get('source_lang', '').strip()
        target_lang = request.args.get('target_lang', '').strip()
        model_used = request.args.get('model', '').strip()

        # 构建查询
        query = TranslationHistory.query.filter_by(user_number=user_number)

        # 添加搜索条件
        if search:
            # 导入必要的模块
            from datetime import datetime, timedelta
            from calendar import monthrange

            # 尝试解析日期格式（只对明显的日期格式进行解析）
            date_conditions = []

            # 只对包含日期分隔符的搜索词进行日期解析
            if '-' in search or '/' in search:
                # 支持多种日期格式
                date_formats = [
                    '%Y-%m-%d',      # 2024-01-01
                    '%Y/%m/%d',      # 2024/01/01
                    '%m-%d',         # 01-01 (当年)
                    '%m/%d',         # 01/01 (当年)
                    '%Y-%m',         # 2024-01
                    '%Y/%m',         # 2024/01
                ]

                for date_format in date_formats:
                    try:
                        if date_format in ['%m-%d', '%m/%d']:
                            # 对于月-日格式，添加当前年份
                            current_year = datetime.now().year
                            search_date = datetime.strptime(f"{current_year}-{search}" if '/' not in search else f"{current_year}/{search}", f"%Y-{date_format}" if '/' not in search else f"%Y/{date_format}")
                        elif date_format in ['%Y-%m', '%Y/%m']:
                            # 对于年-月格式，搜索整个月
                            search_date = datetime.strptime(search, date_format)
                            # 获取月份的开始和结束
                            year = search_date.year
                            month = search_date.month
                            start_date = datetime(year, month, 1)
                            _, last_day = monthrange(year, month)
                            end_date = datetime(year, month, last_day, 23, 59, 59)
                            date_conditions.append(
                                TranslationHistory.created_at.between(start_date, end_date)
                            )
                            break
                        else:
                            search_date = datetime.strptime(search, date_format)

                        if date_format not in ['%Y-%m', '%Y/%m']:
                            # 对于具体日期，搜索整天
                            start_date = search_date.replace(hour=0, minute=0, second=0, microsecond=0)
                            end_date = start_date + timedelta(days=1) - timedelta(microseconds=1)
                            date_conditions.append(
                                TranslationHistory.created_at.between(start_date, end_date)
                            )
                        break
                    except ValueError:
                        continue

            # 构建搜索条件
            search_conditions = [
                TranslationHistory.source_text.contains(search),
                TranslationHistory.translated_text.contains(search),
                TranslationHistory.source_language.contains(search),
                TranslationHistory.target_language.contains(search),
                TranslationHistory.model_used.contains(search)
            ]

            # 添加语言名称搜索（通过语言代码映射）
            for lang_code, lang_name in LANGUAGE_MAP.items():
                if search.lower() in lang_name.lower():
                    search_conditions.extend([
                        TranslationHistory.source_language == lang_code,
                        TranslationHistory.target_language == lang_code
                    ])

            # 添加日期条件
            search_conditions.extend(date_conditions)

            query = query.filter(or_(*search_conditions))

        # 添加语言筛选
        if source_lang:
            query = query.filter_by(source_language=source_lang)
        if target_lang:
            query = query.filter_by(target_language=target_lang)
        if model_used:
            query = query.filter_by(model_used=model_used)

        # 按时间倒序排列并分页
        pagination = query.order_by(TranslationHistory.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        # 格式化返回数据
        history_data = []
        for item in pagination.items:
            data = {
                'id': item.id,
                'source_text': item.source_text,
                'translated_text': item.translated_text,
                'source_language': item.source_language,
                'target_language': item.target_language,
                'model_used': item.model_used,
                'character_count': item.character_count,
                'translation_time': item.translation_time,
                'created_at': item.created_at.isoformat() if item.created_at else None,
                'source_language_name': LANGUAGE_MAP.get(item.source_language, item.source_language),
                'target_language_name': LANGUAGE_MAP.get(item.target_language, item.target_language)
            }
            history_data.append(data)

        return jsonify({
            'status': 'success',
            'data': history_data,
            'pagination': {
                'page': pagination.page,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next
            },
            'languages': LANGUAGE_MAP,
            'models': AVAILABLE_MODELS
        })

    except Exception as e:
        print(f"获取翻译历史失败: {str(e)}")
        return jsonify({'status': 'error', 'message': f'获取翻译历史失败: {str(e)}'}), 500


@translation_bp.route('/user/history/<int:history_id>', methods=['DELETE'])
@require_auth
def delete_user_translation_history(history_id):
    """删除用户的特定翻译历史记录"""
    try:
        # 获取当前用户信息
        user_info = session.get('user_info')
        if not user_info:
            return jsonify({'status': 'error', 'message': '用户未登录'}), 401

        user_number = user_info.get('user_number')
        if not user_number:
            return jsonify({'status': 'error', 'message': '用户信息不完整'}), 400

        # 查找并验证记录所有权
        history_record = TranslationHistory.query.filter_by(
            id=history_id,
            user_number=user_number
        ).first()

        if not history_record:
            return jsonify({'status': 'error', 'message': '记录不存在或无权限删除'}), 404

        # 删除记录
        db.session.delete(history_record)
        db.session.commit()

        return jsonify({'status': 'success', 'message': '翻译记录已删除'})

    except Exception as e:
        db.session.rollback()
        print(f"删除翻译历史失败: {str(e)}")
        return jsonify({'status': 'error', 'message': f'删除失败: {str(e)}'}), 500


@translation_bp.route('/user/history/stats', methods=['GET'])
@require_auth
def get_user_translation_stats():
    """获取用户翻译统计信息"""
    try:
        # 获取当前用户信息
        user_info = session.get('user_info')
        if not user_info:
            return jsonify({'status': 'error', 'message': '用户未登录'}), 401

        user_number = user_info.get('user_number')
        if not user_number:
            return jsonify({'status': 'error', 'message': '用户信息不完整'}), 400

        # 查询用户的翻译统计
        total_translations = TranslationHistory.query.filter_by(user_number=user_number).count()

        # 总字符数
        total_chars = db.session.query(
            db.func.sum(TranslationHistory.character_count)
        ).filter_by(user_number=user_number).scalar() or 0

        # 最常用的语言对
        most_used_lang_pair = db.session.query(
            TranslationHistory.source_language,
            TranslationHistory.target_language,
            db.func.count().label('count')
        ).filter_by(user_number=user_number).group_by(
            TranslationHistory.source_language,
            TranslationHistory.target_language
        ).order_by(db.func.count().desc()).first()

        # 最常用的模型
        most_used_model = db.session.query(
            TranslationHistory.model_used,
            db.func.count().label('count')
        ).filter_by(user_number=user_number).group_by(
            TranslationHistory.model_used
        ).order_by(db.func.count().desc()).first()

        stats = {
            'total_translations': total_translations,
            'total_characters': total_chars,
            'most_used_language_pair': {
                'source': most_used_lang_pair[0] if most_used_lang_pair else None,
                'target': most_used_lang_pair[1] if most_used_lang_pair else None,
                'source_name': LANGUAGE_MAP.get(most_used_lang_pair[0]) if most_used_lang_pair else None,
                'target_name': LANGUAGE_MAP.get(most_used_lang_pair[1]) if most_used_lang_pair else None,
                'count': most_used_lang_pair[2] if most_used_lang_pair else 0
            },
            'most_used_model': {
                'model': most_used_model[0] if most_used_model else None,
                'model_name': AVAILABLE_MODELS.get(most_used_model[0]) if most_used_model else None,
                'count': most_used_model[1] if most_used_model else 0
            }
        }

        return jsonify({
            'status': 'success',
            'data': stats
        })

    except Exception as e:
        print(f"获取翻译统计失败: {str(e)}")
        return jsonify({'status': 'error', 'message': f'获取统计信息失败: {str(e)}'}), 500



<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>河南农业大学智能翻译平台</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="HenauLogo.png">
    <link rel="shortcut icon" type="image/png" href="HenauLogo.png">
    <link rel="apple-touch-icon" href="HenauLogo.png">
    <!-- 本地资源优先，CDN作为备选 -->
    <link href="fallback.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="tailwind.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="vue.js" onerror="this.onerror=null; this.src='https://unpkg.com/vue@3/dist/vue.global.js'"></script>

    <script>
        // 移动端检测和重定向
        function isMobile() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                   window.innerWidth <= 768;
        }

        if (isMobile() && !window.location.pathname.includes('mobile.html')) {
            window.location.href = 'mobile.html';
        }

        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        [v-cloak] {
            display: none;
        }

        .glass-effect {
            backdrop-filter: blur(16px) saturate(180%);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .transition-all {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .bg-blue-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .bg-blue-gradient-light {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }

        .bg-blue-gradient-dark {
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
        }

        .text-shadow {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .hover-scale {
            transition: transform 0.2s ease-in-out;
        }

        .hover-scale:hover {
            transform: scale(1.05);
        }

        /* Loading dots animation */
        .loading-dots {
            display: inline-block;
            animation: loading-dots 1.5s infinite;
        }

        @keyframes loading-dots {

            0%,
            20% {
                opacity: 0;
            }

            50% {
                opacity: 1;
            }

            100% {
                opacity: 0;
            }
        }

        .loading-dots::after {
            content: '...';
            animation: loading-dots-cycle 1.5s infinite;
        }



        @keyframes loading-dots-cycle {
            0% {
                content: '.';
            }

            33% {
                content: '..';
            }

            66% {
                content: '...';
            }

            100% {
                content: '.';
            }
        }

        /* Material Design Button Styles */
        .material-button {
            position: relative;
            overflow: hidden;
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            font-weight: 500;
            font-size: 14px;
            text-transform: none;
            letter-spacing: 0.25px;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            outline: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
        }

        .material-button:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15), 0 2px 6px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .material-button:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
        }

        .material-button:disabled {
            cursor: not-allowed;
            opacity: 0.6;
            transform: none;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        /* Ripple Effect */
        .material-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .material-button:active::before {
            width: 300px;
            height: 300px;
        }

        .material-button-primary {
            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
            color: white;
        }

        .material-button-primary:hover {
            background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
        }

        .material-button-secondary {
            background: linear-gradient(135deg, #42a5f5 0%, #2196f3 100%);
            color: white;
        }

        .material-button-secondary:hover {
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
        }

        .material-button-neutral {
            background: linear-gradient(135deg, #757575 0%, #616161 100%);
            color: white;
        }

        .material-button-neutral:hover {
            background: linear-gradient(135deg, #616161 0%, #424242 100%);
        }

        .material-button-disabled {
            background: linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 100%) !important;
            color: #9e9e9e !important;
        }

        /* Button content positioning */
        .material-button-content {
            position: relative;
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 美化原生下拉框样式 */
        select {
            appearance: none !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%) !important;
            border: 2px solid rgba(99, 102, 241, 0.25) !important;
            border-radius: 16px !important;
            padding: 12px 40px 12px 16px !important;
            font-size: 14px !important;
            font-weight: 500 !important;
            color: #374151 !important;
            cursor: pointer !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.08), 0 2px 4px rgba(0, 0, 0, 0.05) !important;
            outline: none !important;
            position: relative !important;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
            background-repeat: no-repeat !important;
            background-position: right 12px center !important;
            background-size: 16px !important;
        }

        .dark select {
            background: linear-gradient(135deg, rgba(55, 65, 81, 0.95) 0%, rgba(75, 85, 99, 0.95) 100%) !important;
            border-color: rgba(99, 102, 241, 0.35) !important;
            color: #f3f4f6 !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1) !important;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%239ca3af' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
        }

        select:hover {
            border-color: rgba(99, 102, 241, 0.45) !important;
            box-shadow: 0 6px 20px rgba(99, 102, 241, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08) !important;
            transform: translateY(-1px) !important;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236366f1' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
        }

        .dark select:hover {
            border-color: rgba(99, 102, 241, 0.55) !important;
            box-shadow: 0 6px 20px rgba(99, 102, 241, 0.2), 0 4px 8px rgba(0, 0, 0, 0.15) !important;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238b5cf6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
        }

        select:focus {
            border-color: #6366f1 !important;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.12), 0 8px 24px rgba(99, 102, 241, 0.15) !important;
            transform: translateY(-1px) !important;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236366f1' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
        }

        .dark select:focus {
            border-color: #818cf8 !important;
            box-shadow: 0 0 0 3px rgba(129, 140, 248, 0.2), 0 8px 24px rgba(99, 102, 241, 0.25) !important;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23818cf8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
        }

        /* 自定义下拉框组件样式 */
        .custom-dropdown {
            position: relative;
            display: inline-block;
            width: auto;
            min-width: 160px;
            max-width: 200px;
        }

        .custom-dropdown-button {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
            border: 2px solid rgba(99, 102, 241, 0.25);
            border-radius: 16px;
            padding: 12px 40px 12px 16px;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.08), 0 2px 4px rgba(0, 0, 0, 0.05);
            outline: none;
            width: 100%;
            text-align: left;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .dark .custom-dropdown-button {
            background: linear-gradient(135deg, rgba(55, 65, 81, 0.95) 0%, rgba(75, 85, 99, 0.95) 100%);
            border-color: rgba(147, 197, 253, 0.35);
            color: #f3f4f6;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .custom-dropdown-button:hover {
            border-color: rgba(147, 197, 253, 0.6);
            box-shadow: 0 6px 20px rgba(147, 197, 253, 0.15), 0 4px 8px rgba(0, 0, 0, 0.08);
            transform: translateY(-1px);
        }

        .custom-dropdown-button:focus {
            border-color: #60a5fa;
            box-shadow: 0 0 0 3px rgba(147, 197, 253, 0.2), 0 8px 24px rgba(147, 197, 253, 0.15);
            transform: translateY(-1px);
        }

        .custom-dropdown-arrow {
            width: 16px;
            height: 16px;
            transition: transform 0.3s ease;
            color: #6b7280;
        }

        .dark .custom-dropdown-arrow {
            color: #9ca3af;
        }

        .custom-dropdown.open .custom-dropdown-arrow {
            transform: rotate(180deg);
            color: #60a5fa;
        }

        .custom-dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            min-width: 160px;
            max-width: 200px;
            background: rgba(255, 255, 255, 0.98);
            border: 2px solid rgba(147, 197, 253, 0.3);
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1), 0 4px 16px rgba(147, 197, 253, 0.1);
            backdrop-filter: blur(20px);
            z-index: 1000;
            max-height: 280px;
            overflow-y: auto;
            margin-top: 4px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .dark .custom-dropdown-menu {
            background: rgba(55, 65, 81, 0.98);
            border-color: rgba(147, 197, 253, 0.4);
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3), 0 4px 16px rgba(147, 197, 253, 0.2);
        }

        .custom-dropdown.open .custom-dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .custom-dropdown-option {
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #374151;
            font-weight: 500;
            border-radius: 12px;
            margin: 4px 8px;
            display: flex;
            align-items: center;
        }

        .dark .custom-dropdown-option {
            color: #f3f4f6;
        }

        .custom-dropdown-option:hover {
            background: rgba(147, 197, 253, 0.8);
            color: #1e40af;
            transform: translateX(3px);
        }

        .custom-dropdown-option.selected {
            background: rgba(147, 197, 253, 0.9);
            color: #1e40af;
            font-weight: 600;
        }

        .custom-dropdown-menu::-webkit-scrollbar {
            width: 6px;
        }

        .custom-dropdown-menu::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 3px;
        }

        .custom-dropdown-menu::-webkit-scrollbar-thumb {
            background: rgba(147, 197, 253, 0.8);
            border-radius: 3px;
        }

        /* 响应式调整 */
        @media (max-width: 640px) {
            .custom-dropdown {
                min-width: 140px;
                max-width: 180px;
            }

            .custom-dropdown-menu {
                min-width: 140px;
                max-width: 180px;
            }

            select {
                padding: 10px 36px 10px 14px !important;
                font-size: 13px !important;
                border-radius: 12px !important;
                background-size: 14px !important;
                background-position: right 10px center !important;
            }

            select option {
                padding: 10px 14px !important;
                font-size: 13px !important;
            }
        }
    </style>
</head>

<body>
    <div id="app" v-cloak class="min-h-screen"
        :class="isDarkMode ? 'dark bg-blue-gradient-dark' : 'bg-blue-gradient-light'">
        <!-- Header -->
        <header class="p-4 sm:p-6">
            <div class="container mx-auto flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <span class="text-2xl sm:text-3xl"></span>
                    <div>
                        <h1 class="text-white text-lg sm:text-2xl font-bold text-shadow">河南农业大学智能翻译平台</h1>
                        <p class="text-blue-100 text-xs sm:text-sm">AI-Powered Translation Platform</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2 sm:space-x-4">
                    <!-- Dark Mode Toggle -->
                    <button @click="toggleDarkMode"
                        class="p-2 rounded-lg bg-white/20 hover:bg-white/30 text-white transition-all">
                        <i :class="isDarkMode ? 'fas fa-sun' : 'fas fa-moon'"></i>
                    </button>
                    <!-- User Info -->
                    <div v-if="isLoggedIn" class="flex items-center space-x-2 text-white">
                        <span class="text-sm">{{ userInfo.user_name }}</span>
                        <!-- Admin Panel Button (only for admins) -->
                        <a v-if="isAdmin" href="/admin" class="material-button"
                            style="background: rgba(255, 255, 255, 0.15); color: white; padding: 6px 12px; font-size: 12px; backdrop-filter: blur(10px);">
                            <div class="material-button-content">
                                <i class="fas fa-cog sm:mr-1"></i>
                                <span class="hidden sm:inline">管理</span>
                            </div>
                        </a>
                        <button @click="logout" class="material-button"
                            style="background: rgba(255, 255, 255, 0.2); color: white; padding: 6px 12px; font-size: 12px; backdrop-filter: blur(10px);">
                            <div class="material-button-content">
                                <i class="fas fa-sign-out-alt sm:mr-1"></i>
                                <span class="hidden sm:inline">退出</span>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </header>



        <!-- Main Content -->
        <main class="container mx-auto px-4 sm:px-6 pb-12">

            <!-- Translation Interface -->
            <div class="glass-effect bg-white/95 dark:bg-gray-800/95 rounded-2xl p-4 sm:p-6 lg:p-8 border border-white/20">
                <div class="relative">
                    <!-- Swap Button - Responsive positioning -->
                    <div class="hidden xl:block absolute left-1/2 transform -translate-x-1/2 z-10 top-1/2 -translate-y-1/2">
                        <button @click="swapLanguages" class="p-2 focus:outline-none transition-colors
                            text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300">
                            <i class="fas fa-exchange-alt text-primary-600 dark:text-primary-400 text-sm"></i>
                        </button>
                    </div>

                    <div class="grid grid-cols-1 xl:grid-cols-2 gap-6 lg:gap-8 pt-16 xl:pt-0">
                        <!-- Source Text -->
                        <div class="space-y-4">
                            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                                <div class="custom-dropdown" id="sourceLanguageDropdown">
                                    <button class="custom-dropdown-button" @click="toggleDropdown('sourceLanguageDropdown')">
                                        <span>{{ sourceLanguage === 'auto' ? '自动检测' : languages[sourceLanguage] || '自动检测' }}</span>
                                        <svg class="custom-dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div class="custom-dropdown-menu">
                                        <div class="custom-dropdown-option"
                                             :class="{ selected: sourceLanguage === 'auto' }"
                                             @click="selectOption('sourceLanguageDropdown', 'auto', '自动检测')">
                                            自动检测
                                        </div>
                                        <div v-for="(name, code) in languages" v-if="code !== 'auto'" :key="code"
                                             class="custom-dropdown-option"
                                             :class="{ selected: sourceLanguage === code }"
                                             @click="selectOption('sourceLanguageDropdown', code, name)">
                                            {{ name }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="relative">
                                <textarea v-model="sourceText" placeholder="请输入要翻译的文本..."
                                    class="w-full h-48 sm:h-64 p-4 border border-primary-200 dark:border-gray-600 rounded-lg resize-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white bg-gradient-to-br from-white to-primary-50 dark:from-gray-700 dark:to-gray-600 transition-all shadow-inner"
                                    @keydown.ctrl.enter="translateText" :maxlength="maxCharacters"
                                    @input="onSourceTextInput"></textarea>
                                <!-- 字符计数器 -->
                                <div class="absolute bottom-2 right-2 text-xs px-2 py-0.5 rounded-md dark:bg-gray-700/70 shadow-sm backdrop-blur-md"
                                    :class="sourceText.length > maxCharacters * 0.9 ? 'text-red-600' : 'text-gray-500'">
                                    {{ sourceText.length }} / {{ maxCharacters }}
                                </div>
                            </div>

                            <!-- Controls -->
                            <div class="flex flex-col sm:flex-row flex-wrap gap-3">
                                <div class="custom-dropdown" id="modelDropdown">
                                    <button class="custom-dropdown-button" @click="toggleDropdown('modelDropdown')">
                                        <span>{{ models[selectedModel] || '选择模型' }}</span>
                                        <svg class="custom-dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div class="custom-dropdown-menu">
                                        <div v-for="(desc, model) in models" :key="model"
                                             class="custom-dropdown-option"
                                             :class="{ selected: selectedModel === model }"
                                             @click="selectOption('modelDropdown', model, desc)">
                                            {{ desc }}
                                        </div>
                                    </div>
                                </div>

                                <button @click="translateText"
                                    :disabled="!sourceText.trim() || !isLoggedIn || isTranslating"
                                    class="material-button material-button-primary"
                                    :class="{ 'material-button-disabled': !sourceText.trim() || !isLoggedIn || isTranslating }">
                                    <div class="material-button-content">
                                        <i :class="isTranslating ? 'fas fa-spinner fa-spin' : 'fas fa-language'"
                                            class="mr-2"></i>
                                        {{ isTranslating ? '翻译中...' : (!isLoggedIn ? '请先登录' : '翻译') }}
                                    </div>
                                </button>

                                <!-- Inline Swap Button for small/medium screens -->
                                <button @click="swapLanguages"
                                    class="block xl:hidden p-2 focus:outline-none transition-colors text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300">
                                    <i class="fas fa-exchange-alt"></i>
                                </button>

                                <button @click="clearAll" class="material-button material-button-neutral">
                                    <div class="material-button-content">
                                        <i class="fas fa-eraser mr-2"></i>清除
                                    </div>
                                </button>
                            </div>
                        </div>

                        <!-- Target Text -->
                        <div class="space-y-4">
                            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
                                <div class="custom-dropdown" id="targetLanguageDropdown">
                                    <button class="custom-dropdown-button" @click="toggleDropdown('targetLanguageDropdown')">
                                        <span>{{ languages[targetLanguage] || '选择目标语言' }}</span>
                                        <svg class="custom-dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div class="custom-dropdown-menu">
                                        <div v-for="(name, code) in languages" v-if="code !== 'auto'" :key="code"
                                             class="custom-dropdown-option"
                                             :class="{ selected: targetLanguage === code }"
                                             @click="selectOption('targetLanguageDropdown', code, name)">
                                            {{ name }}
                                        </div>
                                    </div>
                                </div>

                                <button @click="copyText(translatedText)" :disabled="!translatedText"
                                    class="px-4 py-2 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-lg transition-all text-sm shadow-lg">
                                    <i class="fas fa-copy mr-1"></i>复制
                                </button>
                            </div>
                            <textarea v-model="translatedText" placeholder="翻译结果将在此显示"
                                class="w-full h-48 sm:h-64 p-4 border border-primary-200 dark:border-gray-600 rounded-lg resize-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white bg-gradient-to-br from-primary-50 to-white dark:from-gray-700 dark:to-gray-600 transition-all shadow-inner"></textarea>

                            <!-- Status -->
                            <div v-if="statusMessage" class="text-sm px-3 py-2 rounded-lg"
                                :class="isTranslating ? 'text-primary-600 bg-primary-50 dark:text-primary-400 dark:bg-primary-900/20' : (statusMessage.includes('成功') ? 'text-primary-700 bg-primary-100 dark:text-primary-300 dark:bg-primary-900/30' : 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-800')">
                                <i v-if="isTranslating" class="fas fa-language mr-2 text-primary-500"></i>
                                <i v-else-if="statusMessage.includes('成功')" class="fas fa-check mr-2"></i>
                                <i v-else class="fas fa-info-circle mr-2"></i>
                                <span v-if="isTranslating">正在翻译中<span class="loading-dots">...</span></span>
                                <span v-else>{{ statusMessage }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </main>
    </div>

    <!-- Vue.js Application -->
    <script>
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    // UI State
                    isDarkMode: localStorage.getItem('darkMode') === 'true',

                    // Auth State
                    isLoggedIn: false,
                    userInfo: {},

                    // Translation State
                    sourceText: '',
                    translatedText: '',
                    sourceLanguage: 'auto',
                    targetLanguage: 'en',
                    selectedModel: 'qwen2.5:14b',
                    isTranslating: false,
                    statusMessage: '',
                    maxCharacters: 5000,
                    detectTimeout: null,

                    // Data - 扩展支持13种语言，繁体中文、简体中文、英语排前面
                    languages: {
                        'zh-tw': '繁体中文',
                        'zh': '简体中文',
                        'en': '英语',
                        'ja': '日语',
                        'ko': '韩语',
                        'fr': '法语',
                        'de': '德语',
                        'es': '西班牙语',
                        'ru': '俄语',
                        'it': '意大利语',
                        'pt': '葡萄牙语',
                        'ar': '阿拉伯语',
                        'hi': '印地语'
                    },
                    models: {}
                }
            },

            computed: {
                isAdmin() {
                    return this.isLoggedIn && ['admin', 'super_admin'].includes(this.userInfo.role);
                }
            },

            async mounted() {
                await this.checkLoginStatus();
                await this.loadAvailableModels();
                this.applyDarkMode();

                // Add click outside listener for dropdowns
                document.addEventListener('click', (e) => {
                    if (!e.target.closest('.custom-dropdown')) {
                        this.closeDropdowns();
                    }
                });
            },

            methods: {
                // Custom Dropdown Methods
                toggleDropdown(dropdownId) {
                    const dropdown = document.getElementById(dropdownId);
                    if (dropdown) {
                        dropdown.classList.toggle('open');

                        // Close other dropdowns
                        document.querySelectorAll('.custom-dropdown').forEach(dd => {
                            if (dd.id !== dropdownId) {
                                dd.classList.remove('open');
                            }
                        });
                    }
                },

                selectOption(dropdownId, value, text) {
                    // Update the model based on dropdown
                    if (dropdownId === 'sourceLanguageDropdown') {
                        this.sourceLanguage = value;
                    } else if (dropdownId === 'targetLanguageDropdown') {
                        this.targetLanguage = value;
                    } else if (dropdownId === 'modelDropdown') {
                        this.selectedModel = value;
                    }

                    // Update button text
                    const button = document.querySelector(`#${dropdownId} .custom-dropdown-button span`);
                    if (button) {
                        button.textContent = text;
                    }

                    // Close dropdown
                    this.toggleDropdown(dropdownId);
                },

                closeDropdowns() {
                    document.querySelectorAll('.custom-dropdown').forEach(dd => {
                        dd.classList.remove('open');
                    });
                },

                // Language Detection
                onSourceTextInput() {
                    if (this.detectTimeout) {
                        clearTimeout(this.detectTimeout);
                    }

                    // 防抖：用户停止输入500ms后检测语言
                    this.detectTimeout = setTimeout(() => {
                        this.detectLanguage();
                    }, 500);
                },

                detectLanguage() {
                    if (this.sourceLanguage !== 'auto' || !this.sourceText.trim()) {
                        return;
                    }

                    const text = this.sourceText.trim();
                    if (text.length < 3) {
                        return;
                    }

                    const detectedLang = this.simpleLanguageDetect(text);

                    if (detectedLang && detectedLang !== 'auto') {
                        this.sourceLanguage = detectedLang;
                    }
                },

                simpleLanguageDetect(text) {
                    // 简单的语言检测规则
                    const chineseRegex = /[\u4e00-\u9fff]/;
                    const japaneseRegex = /[\u3040-\u309f\u30a0-\u30ff]/;
                    const koreanRegex = /[\uac00-\ud7af]/;
                    const arabicRegex = /[\u0600-\u06ff]/;
                    const russianRegex = /[\u0400-\u04ff]/;

                    // 统计各语言字符数量
                    const chineseCount = (text.match(chineseRegex) || []).length;
                    const japaneseCount = (text.match(japaneseRegex) || []).length;
                    const koreanCount = (text.match(koreanRegex) || []).length;
                    const arabicCount = (text.match(arabicRegex) || []).length;
                    const russianCount = (text.match(russianRegex) || []).length;

                    // 如果有明显的非拉丁字符，优先检测
                    if (chineseCount > 0) {
                        // 区分简体中文和繁体中文
                        return this.detectChineseVariant(text);
                    }
                    if (japaneseCount > 0) return 'ja';
                    if (koreanCount > 0) return 'ko';
                    if (arabicCount > 0) return 'ar';
                    if (russianCount > 0) return 'ru';

                    // 对于拉丁字符，进行简单的词汇检测
                    const lowerText = text.toLowerCase();

                    // 常见词汇检测
                    const englishWords = ['the', 'and', 'is', 'in', 'to', 'of', 'a', 'that', 'it', 'with', 'for', 'as', 'was', 'on', 'are', 'you'];
                    const frenchWords = ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir', 'que', 'pour', 'dans', 'ce', 'son', 'une'];
                    const germanWords = ['der', 'die', 'und', 'in', 'den', 'von', 'zu', 'das', 'mit', 'sich', 'des', 'auf', 'für', 'ist', 'im', 'dem'];
                    const spanishWords = ['el', 'la', 'de', 'que', 'y', 'a', 'en', 'un', 'es', 'se', 'no', 'te', 'lo', 'le', 'da', 'su'];
                    const portugueseWords = ['o', 'de', 'a', 'e', 'do', 'da', 'em', 'um', 'para', 'é', 'com', 'não', 'uma', 'os', 'no', 'se'];
                    const italianWords = ['il', 'di', 'che', 'e', 'la', 'per', 'un', 'in', 'con', 'del', 'da', 'a', 'al', 'le', 'si', 'dei'];

                    let englishScore = 0, frenchScore = 0, germanScore = 0, spanishScore = 0, portugueseScore = 0, italianScore = 0;

                    englishWords.forEach(word => {
                        if (lowerText.includes(' ' + word + ' ') || lowerText.startsWith(word + ' ') || lowerText.endsWith(' ' + word)) {
                            englishScore++;
                        }
                    });

                    frenchWords.forEach(word => {
                        if (lowerText.includes(' ' + word + ' ') || lowerText.startsWith(word + ' ') || lowerText.endsWith(' ' + word)) {
                            frenchScore++;
                        }
                    });

                    germanWords.forEach(word => {
                        if (lowerText.includes(' ' + word + ' ') || lowerText.startsWith(word + ' ') || lowerText.endsWith(' ' + word)) {
                            germanScore++;
                        }
                    });

                    spanishWords.forEach(word => {
                        if (lowerText.includes(' ' + word + ' ') || lowerText.startsWith(word + ' ') || lowerText.endsWith(' ' + word)) {
                            spanishScore++;
                        }
                    });

                    portugueseWords.forEach(word => {
                        if (lowerText.includes(' ' + word + ' ') || lowerText.startsWith(word + ' ') || lowerText.endsWith(' ' + word)) {
                            portugueseScore++;
                        }
                    });

                    italianWords.forEach(word => {
                        if (lowerText.includes(' ' + word + ' ') || lowerText.startsWith(word + ' ') || lowerText.endsWith(' ' + word)) {
                            italianScore++;
                        }
                    });

                    // 找出得分最高的语言
                    const scores = {
                        'en': englishScore,
                        'fr': frenchScore,
                        'de': germanScore,
                        'es': spanishScore,
                        'pt': portugueseScore,
                        'it': italianScore
                    };

                    let maxScore = 0;
                    let detectedLang = 'en'; // 默认英语

                    for (const [lang, score] of Object.entries(scores)) {
                        if (score > maxScore) {
                            maxScore = score;
                            detectedLang = lang;
                        }
                    }

                    // 如果得分太低，返回默认英语
                    return maxScore > 0 ? detectedLang : 'en';
                },

                detectChineseVariant(text) {
                    // 区分简体中文和繁体中文
                    // 常见的繁体中文字符
                    const traditionalChars = [
                        '繁', '體', '語', '言', '國', '際', '學', '習', '資', '訊', '電', '腦', '網', '絡',
                        '開', '發', '軟', '體', '應', '用', '設', '計', '創', '新', '技', '術', '專', '業',
                        '經', '濟', '財', '務', '會', '計', '營', '銷', '管', '理', '組', '織', '領', '導',
                        '環', '境', '保', '護', '健', '康', '醫', '療', '藥', '物', '治', '療', '診', '斷',
                        '教', '育', '訓', '練', '課', '程', '學', '生', '老', '師', '學', '校', '大', '學',
                        '時', '間', '歷', '史', '文', '化', '藝', '術', '音', '樂', '電', '影', '書', '籍',
                        '運', '動', '體', '育', '競', '賽', '遊', '戲', '娛', '樂', '旅', '遊', '觀', '光'
                    ];

                    // 常见的简体中文字符
                    const simplifiedChars = [
                        '简', '体', '语', '言', '国', '际', '学', '习', '资', '讯', '电', '脑', '网', '络',
                        '开', '发', '软', '件', '应', '用', '设', '计', '创', '新', '技', '术', '专', '业',
                        '经', '济', '财', '务', '会', '计', '营', '销', '管', '理', '组', '织', '领', '导',
                        '环', '境', '保', '护', '健', '康', '医', '疗', '药', '物', '治', '疗', '诊', '断',
                        '教', '育', '训', '练', '课', '程', '学', '生', '老', '师', '学', '校', '大', '学',
                        '时', '间', '历', '史', '文', '化', '艺', '术', '音', '乐', '电', '影', '书', '籍',
                        '运', '动', '体', '育', '竞', '赛', '游', '戏', '娱', '乐', '旅', '游', '观', '光'
                    ];

                    let traditionalScore = 0;
                    let simplifiedScore = 0;

                    // 统计繁体字符出现次数
                    traditionalChars.forEach(char => {
                        const count = (text.match(new RegExp(char, 'g')) || []).length;
                        traditionalScore += count;
                    });

                    // 统计简体字符出现次数
                    simplifiedChars.forEach(char => {
                        const count = (text.match(new RegExp(char, 'g')) || []).length;
                        simplifiedScore += count;
                    });

                    // 如果繁体字符更多，返回繁体中文
                    if (traditionalScore > simplifiedScore) {
                        return 'zh-tw';
                    } else {
                        // 默认返回简体中文
                        return 'zh';
                    }
                },
                // Dark Mode
                toggleDarkMode() {
                    this.isDarkMode = !this.isDarkMode;
                    localStorage.setItem('darkMode', this.isDarkMode);
                    this.applyDarkMode();
                },

                applyDarkMode() {
                    if (this.isDarkMode) {
                        document.documentElement.classList.add('dark');
                    } else {
                        document.documentElement.classList.remove('dark');
                    }
                },

                // Authentication
                async checkLoginStatus() {
                    try {
                        const response = await fetch('/api/auth/check');
                        const data = await response.json();

                        if (data.logged_in) {
                            this.isLoggedIn = true;
                            this.userInfo = data.user_info;
                        } else {
                            this.isLoggedIn = false;
                            this.userInfo = {};
                        }
                    } catch (error) {
                        console.error('检查登录状态失败:', error);
                        this.isLoggedIn = false;
                        this.userInfo = {};
                    }
                },




                // Logout
                async logout() {
                    try {
                        await fetch('/api/auth/logout', { method: 'POST' });
                        this.isLoggedIn = false;
                        this.userInfo = {};
                        // 退出后跳转到专门的登录页面
                        window.location.href = '/login';
                    } catch (error) {
                        console.error('登出失败:', error);
                        // 即使登出失败也跳转到登录页面
                        window.location.href = '/login';
                    }
                },

                // Load Models
                async loadAvailableModels() {
                    try {
                        const response = await fetch('/api/models');
                        const data = await response.json();

                        if (data.status === 'success') {
                            this.models = data.models;
                            if (data.languages) {
                                // 移除可能带来的 auto 选项，避免下拉框出现重复“自动检测”
                                const { auto, ...otherLangs } = data.languages;
                                this.languages = { ...this.languages, ...otherLangs };
                            }
                        }
                    } catch (error) {
                        console.error('加载模型列表失败:', error);
                        // Fallback models
                        this.models = {
                            'qwen2.5:14b': '通义千问2.5 14B',
                            'qwen2:7b': '通义千问2 7B',
                            'qwen2.5:32b': '通义千问2.5 32B',
                            'gemma3:27b': 'Gemma 3 27B',
                            'deepseek-r1:14b': 'DeepSeek R1 14B'
                        }
                    }
                },

                // Translation
                async translate() {
                    if (!this.sourceText.trim()) {
                        this.showNotification('请输入要翻译的文本', 'warning');
                        return;
                    }

                    this.isTranslating = true;
                    this.translatedText = '';
                    this.statusMessage = '正在翻译中'; // 设置状态消息，动画点在模板中处理

                    try {
                        const response = await fetch('/api/translate', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                text: this.sourceText,
                                source_lang: this.sourceLanguage,
                                target_lang: this.targetLanguage,
                                model: this.selectedModel
                            })
                        });

                        const data = await response.json();

                        if (data.status === 'success') {
                            this.translatedText = data.translation;
                            this.statusMessage = '翻译完成';
                            // 移除成功通知 - 用户已能看到翻译结果
                        } else {
                            console.warn('Translation error:', data.message);
                            this.statusMessage = '翻译失败，请稍后重试';

                        }
                    } catch (error) {
                        console.error('翻译失败:', error);
                        this.statusMessage = '网络错误，请重试';
                        this.showNotification('网络错误，请重试', 'warning');
                    } finally {
                        this.isTranslating = false;
                    }
                },

                // Copy text
                async copyText(text) {
                    try {
                        await navigator.clipboard.writeText(text);
                        this.showNotification('已复制到剪贴板', 'success');
                    } catch (error) {
                        console.error('复制失败:', error);
                        this.showNotification('复制失败', 'error');
                    }
                },

                // Clear text
                clearSource() {
                    this.sourceText = '';
                },

                clearTranslation() {
                    this.translatedText = '';
                    this.statusMessage = '';
                },

                // Swap languages
                swapLanguages() {
                    if (this.sourceLanguage !== 'auto' && this.targetLanguage !== 'auto') {
                        const temp = this.sourceLanguage;
                        this.sourceLanguage = this.targetLanguage;
                        this.targetLanguage = temp;

                        // Also swap text if both are available
                        if (this.translatedText) {
                            const tempText = this.sourceText;
                            this.sourceText = this.translatedText;
                            this.translatedText = tempText;
                        }
                    }
                },

                // Demo mode removed - mandatory authentication required



                // Alias methods for template bindings
                translateText() {
                    this.translate();
                },

                clearAll() {
                    this.clearSource();
                    this.clearTranslation();
                },

                showNotification(message, type = 'info') {
                    // Create notification element
                    const notification = document.createElement('div');
                    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 text-white transition-all duration-300 transform translate-x-full`;

                    switch (type) {
                        case 'success':
                            notification.classList.add('bg-green-500');
                            break;
                        case 'error':
                            notification.classList.add('bg-red-500');
                            break;
                        case 'warning':
                            notification.classList.add('bg-yellow-500');
                            break;
                        default:
                            notification.classList.add('bg-blue-500');
                    }

                    notification.textContent = message;
                    document.body.appendChild(notification);

                    // Animate in
                    setTimeout(() => {
                        notification.classList.remove('translate-x-full');
                    }, 100);

                    // Remove after 3 seconds
                    setTimeout(() => {
                        notification.classList.add('translate-x-full');
                        setTimeout(() => {
                            document.body.removeChild(notification);
                        }, 300);
                    }, 3000);
                },

                // Enhanced translate method with character limit check
                async translateText() {
                    if (!this.sourceText.trim()) {
                        this.showNotification('请输入要翻译的文本', 'warning');
                        return;
                    }

                    if (this.sourceText.length > this.maxCharacters) {
                        this.showNotification(`文本长度超过限制，最多支持${this.maxCharacters}个字符`, 'error');
                        return;
                    }

                    await this.translate();
                }
            }
        }).mount('#app');
    </script>
</body>

</html>
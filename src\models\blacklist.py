from .db import db
from datetime import datetime
import pytz

class Blacklist(db.Model):
    __tablename__ = "blacklist"

    id = db.<PERSON>umn(db.Integer, primary_key=True)
    user_number = db.Column(db.String(20), nullable=False, index=True)
    username = db.Column(db.String(80), nullable=True)
    user_section = db.Column(db.String(100), nullable=True)
    # 封禁原因
    reason = db.Column(db.Text, nullable=True)
    # 封禁操作者
    banned_by = db.Column(db.String(20), nullable=False)  # 操作管理员的user_number
    banned_by_name = db.Column(db.String(80), nullable=True)  # 操作管理员姓名
    # 封禁时间
    banned_at = db.Column(db.DateTime, nullable=False, default=lambda: datetime.now(pytz.timezone('Asia/Shanghai')))
    # 是否有效（支持解封）
    is_active = db.Column(db.<PERSON>, nullable=False, default=True)
    # 解封时间和操作者
    unbanned_at = db.Column(db.DateTime, nullable=True)
    unbanned_by = db.Column(db.String(20), nullable=True)
    unbanned_by_name = db.Column(db.String(80), nullable=True)

    def to_dict(self):
        return {
            "id": self.id,
            "user_number": self.user_number,
            "username": self.username,
            "user_section": self.user_section,
            "reason": self.reason,
            "banned_by": self.banned_by,
            "banned_by_name": self.banned_by_name,
            "banned_at": self.banned_at.isoformat() if self.banned_at else None,
            "is_active": self.is_active,
            "unbanned_at": self.unbanned_at.isoformat() if self.unbanned_at else None,
            "unbanned_by": self.unbanned_by,
            "unbanned_by_name": self.unbanned_by_name,
        }

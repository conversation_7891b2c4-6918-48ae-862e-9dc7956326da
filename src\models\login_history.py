"""
登录历史记录模型
"""

from datetime import datetime
import pytz
from .db import db

class LoginHistory(db.Model):
    """登录历史记录"""
    __tablename__ = 'login_history'

    id = db.Column(db.Integer, primary_key=True)
    user_number = db.Column(db.String(100), nullable=False, index=True)
    user_name = db.Column(db.String(100), nullable=False)
    user_section = db.Column(db.String(100), nullable=True)
    login_type = db.Column(db.String(20), default='qr_code')  # qr_code, oauth等
    login_time = db.Column(db.DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Shanghai')))
    logout_time = db.Column(db.DateTime, nullable=True)
    session_duration = db.Column(db.Integer, nullable=True)  # 会话持续时间（秒）
    ip_address = db.Column(db.String(45), nullable=True)  # 支持IPv6
    user_agent = db.Column(db.String(500), nullable=True)
    is_active = db.Column(db.<PERSON>an, default=True)  # 会话是否活跃
    
    def __repr__(self):
        return f'<LoginHistory {self.id}: {self.user_name} at {self.login_time}>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_number': self.user_number,
            'user_name': self.user_name,
            'user_section': self.user_section,
            'login_type': self.login_type,
            'login_time': self.login_time.isoformat() if self.login_time else None,
            'logout_time': self.logout_time.isoformat() if self.logout_time else None,
            'session_duration': self.session_duration,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'is_active': self.is_active
        }
    
    @classmethod
    def log_login(cls, user_number, user_name, user_section=None, ip_address=None,
                  user_agent=None, login_type='qr_code'):
        """记录登录历史"""
        history = cls(
            user_number=user_number,
            user_name=user_name,
            user_section=user_section,
            login_type=login_type,
            ip_address=ip_address,
            user_agent=user_agent,
            is_active=True
        )
        db.session.add(history)
        return history
